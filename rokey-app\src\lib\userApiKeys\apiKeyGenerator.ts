import { encrypt, decrypt } from '@/lib/encryption';

export class Api<PERSON>eyGenerator {
  private static readonly KEY_PREFIX = 'rk_live_';
  private static readonly RANDOM_PART_LENGTH = 8; // hex chars for middle part
  private static readonly SECRET_PART_LENGTH = 32; // chars for secret part

  /**
   * Generates a new API key with the format: rk_live_{8_hex_chars}_{32_random_chars}
   * @returns Object containing the full key, prefix, and secret parts
   */
  static async generateApiKey(): Promise<{
    fullKey: string;
    prefix: string;
    secretPart: string;
    hash: string;
  }> {
    // Generate random hex for the middle part (visible in prefix)
    const randomBytes = crypto.getRandomValues(new Uint8Array(this.RANDOM_PART_LENGTH / 2));
    const randomHex = Array.from(randomBytes, byte => byte.toString(16).padStart(2, '0')).join('');

    // Generate random alphanumeric for the secret part
    const secretPart = this.generateRandomString(this.SECRET_PART_LENGTH);

    // Construct the full key
    const prefix = `${this.KEY_PREFIX}${randomHex}`;
    const fullKey = `${prefix}_${secretPart}`;

    // Generate hash for storage
    const hash = await this.hashApiKey(fullKey);

    return {
      fullKey,
      prefix,
      secretPart,
      hash
    };
  }

  /**
   * Generates a cryptographically secure random string
   * @param length Length of the string to generate
   * @returns Random alphanumeric string
   */
  private static generateRandomString(length: number): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';

    for (let i = 0; i < length; i++) {
      const randomBytes = crypto.getRandomValues(new Uint8Array(1));
      const randomIndex = randomBytes[0] % chars.length;
      result += chars[randomIndex];
    }

    return result;
  }

  /**
   * Creates a SHA-256 hash of the API key for secure storage
   * @param apiKey The full API key to hash
   * @returns SHA-256 hash as hex string
   */
  static async hashApiKey(apiKey: string): Promise<string> {
    const encoder = new TextEncoder();
    const data = encoder.encode(apiKey);
    const hashBuffer = await crypto.subtle.digest('SHA-256', data);
    const hashArray = new Uint8Array(hashBuffer);
    return Array.from(hashArray, byte => byte.toString(16).padStart(2, '0')).join('');
  }

  /**
   * Validates the format of an API key
   * @param apiKey The API key to validate
   * @returns True if the format is valid
   */
  static isValidFormat(apiKey: string): boolean {
    const pattern = new RegExp(`^${this.KEY_PREFIX}[a-f0-9]{${this.RANDOM_PART_LENGTH}}_[a-zA-Z0-9]{${this.SECRET_PART_LENGTH}}$`);
    return pattern.test(apiKey);
  }

  /**
   * Extracts the prefix from a full API key
   * @param apiKey The full API key
   * @returns The prefix part (e.g., "rk_live_abc12345")
   */
  static extractPrefix(apiKey: string): string {
    const parts = apiKey.split('_');
    if (parts.length >= 3) {
      return `${parts[0]}_${parts[1]}_${parts[2]}`;
    }
    return '';
  }

  /**
   * Encrypts the suffix part of an API key for partial display
   * @param secretPart The secret part of the API key
   * @returns Encrypted suffix for storage
   */
  static async encryptSuffix(secretPart: string): Promise<string> {
    // Take last 4 characters for display purposes
    const suffix = secretPart.slice(-4);
    return await encrypt(suffix);
  }

  /**
   * Decrypts the suffix for display
   * @param encryptedSuffix The encrypted suffix from database
   * @returns Decrypted suffix for display
   */
  static async decryptSuffix(encryptedSuffix: string): Promise<string> {
    try {
      return await decrypt(encryptedSuffix);
    } catch (error) {
      console.error('Failed to decrypt API key suffix:', error);
      // Return a placeholder that looks like the last 4 chars
      return 'xxxx';
    }
  }

  /**
   * Creates a masked version of the API key for display
   * @param prefix The key prefix
   * @param encryptedSuffix The encrypted suffix
   * @returns Masked key for display (e.g., "rk_live_abc12345_****xyz")
   */
  static async createMaskedKey(prefix: string, encryptedSuffix: string): Promise<string> {
    const suffix = await this.decryptSuffix(encryptedSuffix);
    // Show 4 chars at the end, mask the rest (32 - 4 = 28 chars to mask)
    const maskedLength = this.SECRET_PART_LENGTH - 4;
    return `${prefix}_${'*'.repeat(maskedLength)}${suffix}`;
  }

  /**
   * Validates subscription tier limits for API key generation
   * @param subscriptionTier User's subscription tier
   * @param currentKeyCount Current number of API keys for the user
   * @returns Object indicating if generation is allowed and any limits
   */
  static validateSubscriptionLimits(
    subscriptionTier: string,
    currentKeyCount: number
  ): {
    allowed: boolean;
    limit: number;
    message?: string;
  } {
    const limits = {
      starter: 5,
      professional: 25,
      enterprise: 100
    };

    const limit = limits[subscriptionTier as keyof typeof limits] || limits.starter;

    if (currentKeyCount >= limit) {
      return {
        allowed: false,
        limit,
        message: `You have reached the maximum number of API keys (${limit}) for your ${subscriptionTier} plan.`
      };
    }

    return {
      allowed: true,
      limit
    };
  }


}
