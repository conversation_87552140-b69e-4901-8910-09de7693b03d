import { type NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClientFromRequest } from '@/lib/supabase/server';
import { ApiKeyGenerator } from '@/lib/userApiKeys/apiKeyGenerator';
import { z } from 'zod';
import type {
  CreateUserApiKeyRequest,
  GeneratedApiKeyResponse,
  UserGeneratedApiKey
} from '@/types/userApiKeys';

// Validation schema for creating API keys
const CreateApiKeySchema = z.object({
  custom_api_config_id: z.string().uuid(),
  key_name: z.string().min(1).max(100),
  expires_at: z.string().datetime().optional()
});

// POST /api/user-api-keys - Generate a new API key
export async function POST(request: NextRequest) {
  const supabase = createSupabaseServerClientFromRequest(request);

  try {
    // Authenticate user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse and validate request body
    const body = await request.json();
    const validatedData = CreateApiKeySchema.parse(body);

    // Check if user owns the custom API config
    const { data: config, error: configError } = await supabase
      .from('custom_api_configs')
      .select('id, name, user_id')
      .eq('id', validatedData.custom_api_config_id)
      .eq('user_id', user.id)
      .single();

    if (configError || !config) {
      return NextResponse.json({ 
        error: 'Custom API configuration not found or access denied' 
      }, { status: 404 });
    }

    // Get user's subscription tier from subscriptions table
    const { data: subscription } = await supabase
      .from('subscriptions')
      .select('tier')
      .eq('user_id', user.id)
      .eq('status', 'active')
      .single();

    const subscriptionTier = subscription?.tier || 'starter';

    // Check current API key count for this user
    const { count: currentKeyCount } = await supabase
      .from('user_generated_api_keys')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', user.id)
      .eq('status', 'active');

    // Validate subscription limits
    const limitCheck = ApiKeyGenerator.validateSubscriptionLimits(
      subscriptionTier,
      currentKeyCount || 0
    );

    if (!limitCheck.allowed) {
      return NextResponse.json({ 
        error: limitCheck.message 
      }, { status: 403 });
    }

    // Check for duplicate key name within the config
    const { data: existingKey } = await supabase
      .from('user_generated_api_keys')
      .select('id')
      .eq('custom_api_config_id', validatedData.custom_api_config_id)
      .eq('key_name', validatedData.key_name)
      .eq('status', 'active')
      .single();

    if (existingKey) {
      return NextResponse.json({ 
        error: 'An API key with this name already exists for this configuration' 
      }, { status: 409 });
    }

    // Generate the API key
    const { fullKey, prefix, secretPart, hash } = await ApiKeyGenerator.generateApiKey();
    const encryptedSuffix = await ApiKeyGenerator.encryptSuffix(secretPart);

    // Prepare the API key data with default settings
    const apiKeyData = {
      user_id: user.id,
      custom_api_config_id: validatedData.custom_api_config_id,
      key_name: validatedData.key_name,
      key_prefix: prefix,
      key_hash: hash,
      encrypted_key_suffix: encryptedSuffix,
      permissions: {
        chat: true,
        streaming: true,
        all_models: true
      },
      allowed_ips: [],
      allowed_domains: [],
      expires_at: validatedData.expires_at || null
    };

    // Insert the API key into the database
    const { data: createdKey, error: insertError } = await supabase
      .from('user_generated_api_keys')
      .insert(apiKeyData)
      .select()
      .single();

    if (insertError) {
      console.error('Error creating API key:', insertError);
      return NextResponse.json({ 
        error: 'Failed to create API key' 
      }, { status: 500 });
    }

    // Return the response with the full API key (only shown once)
    const response: GeneratedApiKeyResponse = {
      id: createdKey.id,
      key_name: createdKey.key_name,
      api_key: fullKey, // Full key - only shown once!
      key_prefix: createdKey.key_prefix,
      permissions: createdKey.permissions,
      created_at: createdKey.created_at,
      expires_at: createdKey.expires_at
    };

    return NextResponse.json(response, { status: 201 });

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ 
        error: 'Invalid request data',
        details: error.errors 
      }, { status: 400 });
    }

    console.error('Error in POST /api/user-api-keys:', error);
    return NextResponse.json({
      error: 'Internal server error'
    }, { status: 500 });
  }
}

// GET /api/user-api-keys - List user's API keys
export async function GET(request: NextRequest) {
  const supabase = createSupabaseServerClientFromRequest(request);

  try {
    // Authenticate user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const configId = searchParams.get('config_id');

    // Build query
    let query = supabase
      .from('user_generated_api_keys')
      .select(`
        id,
        key_name,
        key_prefix,
        encrypted_key_suffix,
        permissions,
        allowed_ips,
        allowed_domains,
        total_requests,
        last_used_at,
        status,
        expires_at,
        created_at,
        custom_api_configs!inner(
          id,
          name
        )
      `)
      .eq('user_id', user.id)
      .order('created_at', { ascending: false });

    // Filter by config if specified
    if (configId) {
      query = query.eq('custom_api_config_id', configId);
    }

    const { data: apiKeys, error } = await query;

    if (error) {
      console.error('Error fetching API keys:', error);
      return NextResponse.json({
        error: 'Failed to fetch API keys'
      }, { status: 500 });
    }

    // Transform the data to include masked keys
    const transformedKeys = apiKeys.map((key: any) => ({
      ...key,
      masked_key: ApiKeyGenerator.createMaskedKey(key.key_prefix, key.encrypted_key_suffix),
      // Remove sensitive data
      encrypted_key_suffix: undefined
    }));

    return NextResponse.json({ api_keys: transformedKeys });

  } catch (error) {
    console.error('Error in GET /api/user-api-keys:', error);
    return NextResponse.json({
      error: 'Internal server error'
    }, { status: 500 });
  }
}
