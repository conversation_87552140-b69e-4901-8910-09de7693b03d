import { type NextRequest, NextResponse } from 'next/server';
// cookies function is not directly needed here if createSupabaseServerClientOnRequest handles it
import { createSupabaseServerClientFromRequest } from '@/lib/supabase/server';
import { encrypt } from '@/lib/encryption'; // encrypt returns a single string now
import { type NewApiKey, type ApiKey, type DisplayApiKey } from '@/types/apiKeys';
import crypto from 'crypto'; // Import Node.js crypto module

// POST /api/keys
// Adds a new API key to a specific custom_api_config
export async function POST(request: NextRequest) {
  const supabase = createSupabaseServerClientFromRequest(request);

  // Get authenticated user (more secure than getSession)
  const { data: { user }, error: authError } = await supabase.auth.getUser();
  if (authError || !user) {
    console.error('Authentication error in POST /api/keys:', authError);
    return NextResponse.json({ error: 'Unauthorized: You must be logged in to create API keys.' }, { status: 401 });
  }

  try {
    const keyData = await request.json() as NewApiKey;
    const { custom_api_config_id, provider, predefined_model_id, api_key_raw, label, temperature = 1.0 } = keyData;

    if (!custom_api_config_id || !provider || !predefined_model_id || !api_key_raw || !label) {
      return NextResponse.json({ error: 'Missing required fields: custom_api_config_id, provider, predefined_model_id, api_key_raw, label' }, { status: 400 });
    }

    // Validate temperature
    if (temperature < 0.0 || temperature > 2.0) {
      return NextResponse.json({ error: 'Temperature must be between 0.0 and 2.0' }, { status: 400 });
    }

    if (typeof api_key_raw !== 'string' || api_key_raw.trim().length === 0) {
        return NextResponse.json({ error: 'API key cannot be empty.' }, { status: 400 });
    }
    
    // ROKEY_ENCRYPTION_KEY is checked within encryption.ts now, but good to be aware of its necessity.
    // const encryptionKey = process.env.ROKEY_ENCRYPTION_KEY;
    // if (!encryptionKey) {
    //   console.error('ROKEY_ENCRYPTION_KEY is not set.');
    //   return NextResponse.json({ error: 'Server configuration error: Encryption key not found.' }, { status: 500 });
    // }

    // encrypt function now returns a single string: iv:authTag:encryptedData
    const encrypted_api_key_combined = await encrypt(api_key_raw);
    const api_key_hash = crypto.createHash('sha256').update(api_key_raw).digest('hex');

    // The type Omit<ApiKey, ...> will align because ApiKey no longer has 'iv'
    const newDbKey: Omit<ApiKey, 'id' | 'created_at' | 'updated_at' | 'last_used_at'> = {
      custom_api_config_id,
      provider,
      predefined_model_id,
      encrypted_api_key: encrypted_api_key_combined, // Store the combined string
      label,
      api_key_hash,
      status: 'active',
      is_default_general_chat_model: false,
      temperature,
      user_id: user.id, // Set the user_id for RLS policy compliance
    };

    const { data, error } = await supabase
      .from('api_keys')
      .insert(newDbKey)
      .select()
      .single();

    if (error) {
      console.error('Supabase error creating API key:', error);
      if (error.code === '23503') {
        return NextResponse.json({ error: 'Invalid custom_api_config_id or predefined_model_id.', details: error.message }, { status: 400 });
      }
      if (error.code === '23505') {
        // Check if this is the new unique_model_per_config constraint
        if (error.message.includes('unique_model_per_config')) {
          return NextResponse.json({
            error: 'This model is already configured in this setup. Each model can only be used once per configuration, but you can use the same API key with different models.',
            details: error.message
          }, { status: 409 });
        }
        // Fallback for other unique constraint violations
        return NextResponse.json({ error: 'A unique constraint was violated.', details: error.message }, { status: 409 });
      }
      return NextResponse.json({ error: 'Failed to save API key', details: error.message }, { status: 500 });
    }

    // If this is the first key for the config, or no other key is default, make this one default.
    if (data) {
      const { data: existingDefaults, error: defaultCheckError } = await supabase
        .from('api_keys')
        .select('id')
        .eq('custom_api_config_id', custom_api_config_id)
        .eq('user_id', user.id) // Filter by user_id for RLS compliance
        .eq('is_default_general_chat_model', true)
        .neq('id', data.id) // Exclude the newly added key itself from this check
        .limit(1);

      if (defaultCheckError) {
        console.error('Error checking for existing default keys:', defaultCheckError);
        // Proceed without making it default, but log the error. The key is still saved.
      } else if (!existingDefaults || existingDefaults.length === 0) {
        // No other key is default, so make this new one default
        const { data: updatedKey, error: updateError } = await supabase
          .from('api_keys')
          .update({ is_default_general_chat_model: true })
          .eq('id', data.id)
          .eq('user_id', user.id) // Filter by user_id for RLS compliance
          .select()
          .single();
        
        if (updateError) {
          console.error('Error updating new key to be default:', updateError);
          // Key is saved, but failed to make it default. Log and proceed.
        } else {
          // Successfully made the new key default, return this updated key data
          return NextResponse.json(updatedKey as ApiKey, { status: 201 });
        }
      }
    }

    return NextResponse.json(data as ApiKey, { status: 201 });

  } catch (e: any) {
    console.error('Error in POST /api/keys:', e);
     if (e.name === 'SyntaxError') { 
        return NextResponse.json({ error: 'Invalid request body: Malformed JSON.' }, { status: 400 });
    }
    // Catch errors from encrypt function (e.g., if ROKEY_ENCRYPTION_KEY was invalid)
    if (e.message.includes('Invalid ROKEY_ENCRYPTION_KEY') || e.message.includes('Encryption input must be a non-empty string')) {
        return NextResponse.json({ error: 'Server-side encryption error', details: e.message }, { status: 500 });
    }
    return NextResponse.json({ error: 'An unexpected error occurred', details: e.message }, { status: 500 });
  }
}

// GET /api/keys?custom_config_id=<ID>
// Retrieves all API keys for a specific custom_api_config_id
export async function GET(request: NextRequest) {
  const supabase = createSupabaseServerClientFromRequest(request);

  // Get authenticated user (more secure than getSession)
  const { data: { user }, error: authError } = await supabase.auth.getUser();
  if (authError || !user) {
    console.error('Authentication error in GET /api/keys:', authError);
    return NextResponse.json({ error: 'Unauthorized: You must be logged in to view API keys.' }, { status: 401 });
  }

  const { searchParams } = new URL(request.url);
  const customConfigId = searchParams.get('custom_config_id');

  if (!customConfigId) {
    return NextResponse.json({ error: 'custom_config_id query parameter is required' }, { status: 400 });
  }

  try {
    const { data: keys, error } = await supabase
      // Ensure selected fields match DisplayApiKey definition
      .from('api_keys')
      .select('id, custom_api_config_id, provider, predefined_model_id, label, status, temperature, created_at, last_used_at, is_default_general_chat_model')
      .eq('custom_api_config_id', customConfigId)
      .eq('user_id', user.id) // Filter by user_id for RLS compliance
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Supabase error fetching API keys:', error);
      return NextResponse.json({ error: 'Failed to fetch API keys', details: error.message }, { status: 500 });
    }

    const displayKeys: DisplayApiKey[] = (keys || []).map((key: any) => ({
        id: key.id,
        custom_api_config_id: key.custom_api_config_id,
        provider: key.provider,
        predefined_model_id: key.predefined_model_id,
        label: key.label,
        status: key.status,
        temperature: key.temperature,
        created_at: key.created_at,
        last_used_at: key.last_used_at,
        is_default_general_chat_model: key.is_default_general_chat_model,
    }));

    return NextResponse.json(displayKeys, { status: 200 });

  } catch (e: any) {
    console.error('Error in GET /api/keys:', e);
    return NextResponse.json({ error: 'An unexpected error occurred', details: e.message }, { status: 500 });
  }
}

// PUT /api/keys?id=<ID>
// Updates an existing API key (currently supports temperature updates)
export async function PUT(request: NextRequest) {
  const supabase = createSupabaseServerClientFromRequest(request);

  // Get authenticated user from session
  const { data: { session }, error: sessionError } = await supabase.auth.getSession();
  if (sessionError || !session?.user) {
    console.error('Authentication error in PUT /api/keys:', sessionError);
    return NextResponse.json({ error: 'Unauthorized: You must be logged in to update API keys.' }, { status: 401 });
  }

  const user = session.user;

  const { searchParams } = new URL(request.url);
  const keyId = searchParams.get('id');

  if (!keyId) {
    return NextResponse.json({ error: 'id query parameter is required' }, { status: 400 });
  }

  try {
    const updateData = await request.json();
    const { temperature, predefined_model_id } = updateData;

    // Prepare update object
    const updateFields: any = {};

    if (temperature !== undefined) {
      // Validate temperature
      if (temperature < 0.0 || temperature > 2.0) {
        return NextResponse.json({ error: 'Temperature must be between 0.0 and 2.0' }, { status: 400 });
      }
      updateFields.temperature = temperature;
    }

    if (predefined_model_id !== undefined) {
      if (typeof predefined_model_id !== 'string' || predefined_model_id.trim().length === 0) {
        return NextResponse.json({ error: 'Model ID must be a non-empty string' }, { status: 400 });
      }
      updateFields.predefined_model_id = predefined_model_id;
    }

    // If no fields to update, return error
    if (Object.keys(updateFields).length === 0) {
      return NextResponse.json({ error: 'No valid fields provided for update' }, { status: 400 });
    }

    const { data, error } = await supabase
      .from('api_keys')
      .update(updateFields)
      .eq('id', keyId)
      .eq('user_id', user.id) // Filter by user_id for RLS compliance
      .select('id, custom_api_config_id, provider, predefined_model_id, label, status, temperature, created_at, last_used_at, is_default_general_chat_model')
      .single();

    if (error) {
      console.error('Supabase error updating API key:', error);
      if (error.code === '23505' && error.message.includes('unique_model_per_config')) {
        return NextResponse.json({
          error: 'This model is already configured in this setup. Each model can only be used once per configuration.',
          details: error.message
        }, { status: 409 });
      }
      return NextResponse.json({ error: 'Failed to update API key', details: error.message }, { status: 500 });
    }

    return NextResponse.json(data, { status: 200 });

  } catch (e: any) {
    console.error('Error in PUT /api/keys:', e);
    if (e.name === 'SyntaxError') {
      return NextResponse.json({ error: 'Invalid request body: Malformed JSON.' }, { status: 400 });
    }
    return NextResponse.json({ error: 'An unexpected error occurred', details: e.message }, { status: 500 });
  }
}