1:"$Sreact.fragment"
2:"$Sreact.suspense"
3:I[99030,["8888","static/chunks/supabase-55776fae-704411a5287a30d1.js","1459","static/chunks/supabase-0d08456b-7b71d5b8f6389e71.js","5738","static/chunks/utils-465036887cb9f2fe.js","9968","static/chunks/ui-components-b80f15b3-ba6d86bdab1b54cc.js","6060","static/chunks/ui-components-3acb5f41-600bd31c9d374c04.js","6308","static/chunks/vendors-7237a82e-5b1f53a4b8780d18.js","563","static/chunks/vendors-ad6a2f20-97d1446c887ff15f.js","2662","static/chunks/vendors-04fef8b0-2eb254c773caab4c.js","8669","static/chunks/vendors-7ec938a2-dcd0e26d54d6db3d.js","8848","static/chunks/vendors-2ced652b-85aee2751f317897.js","622","static/chunks/vendors-b49fab05-31bf43a75248b59e.js","2432","static/chunks/vendors-27f02048-d5ca4f1bc0e362d3.js","408","static/chunks/vendors-fa70753b-237bdcd83400e2f4.js","9558","static/chunks/app/layout-458df6b196458b9c.js"],"default"]
4:I[52469,["8888","static/chunks/supabase-55776fae-704411a5287a30d1.js","1459","static/chunks/supabase-0d08456b-7b71d5b8f6389e71.js","5738","static/chunks/utils-465036887cb9f2fe.js","9968","static/chunks/ui-components-b80f15b3-ba6d86bdab1b54cc.js","6060","static/chunks/ui-components-3acb5f41-600bd31c9d374c04.js","6308","static/chunks/vendors-7237a82e-5b1f53a4b8780d18.js","563","static/chunks/vendors-ad6a2f20-97d1446c887ff15f.js","2662","static/chunks/vendors-04fef8b0-2eb254c773caab4c.js","8669","static/chunks/vendors-7ec938a2-dcd0e26d54d6db3d.js","8848","static/chunks/vendors-2ced652b-85aee2751f317897.js","622","static/chunks/vendors-b49fab05-31bf43a75248b59e.js","2432","static/chunks/vendors-27f02048-d5ca4f1bc0e362d3.js","408","static/chunks/vendors-fa70753b-237bdcd83400e2f4.js","9558","static/chunks/app/layout-458df6b196458b9c.js"],"default"]
5:I[38050,["8888","static/chunks/supabase-55776fae-704411a5287a30d1.js","1459","static/chunks/supabase-0d08456b-7b71d5b8f6389e71.js","5738","static/chunks/utils-465036887cb9f2fe.js","9968","static/chunks/ui-components-b80f15b3-ba6d86bdab1b54cc.js","6060","static/chunks/ui-components-3acb5f41-600bd31c9d374c04.js","6308","static/chunks/vendors-7237a82e-5b1f53a4b8780d18.js","563","static/chunks/vendors-ad6a2f20-97d1446c887ff15f.js","2662","static/chunks/vendors-04fef8b0-2eb254c773caab4c.js","8669","static/chunks/vendors-7ec938a2-dcd0e26d54d6db3d.js","8848","static/chunks/vendors-2ced652b-85aee2751f317897.js","622","static/chunks/vendors-b49fab05-31bf43a75248b59e.js","2432","static/chunks/vendors-27f02048-d5ca4f1bc0e362d3.js","408","static/chunks/vendors-fa70753b-237bdcd83400e2f4.js","9558","static/chunks/app/layout-458df6b196458b9c.js"],"default"]
6:I[83434,["8888","static/chunks/supabase-55776fae-704411a5287a30d1.js","1459","static/chunks/supabase-0d08456b-7b71d5b8f6389e71.js","5738","static/chunks/utils-465036887cb9f2fe.js","9968","static/chunks/ui-components-b80f15b3-ba6d86bdab1b54cc.js","6060","static/chunks/ui-components-3acb5f41-600bd31c9d374c04.js","6308","static/chunks/vendors-7237a82e-5b1f53a4b8780d18.js","563","static/chunks/vendors-ad6a2f20-97d1446c887ff15f.js","2662","static/chunks/vendors-04fef8b0-2eb254c773caab4c.js","8669","static/chunks/vendors-7ec938a2-dcd0e26d54d6db3d.js","8848","static/chunks/vendors-2ced652b-85aee2751f317897.js","622","static/chunks/vendors-b49fab05-31bf43a75248b59e.js","2432","static/chunks/vendors-27f02048-d5ca4f1bc0e362d3.js","408","static/chunks/vendors-fa70753b-237bdcd83400e2f4.js","9558","static/chunks/app/layout-458df6b196458b9c.js"],"default"]
7:I[87555,[],""]
8:I[31295,[],""]
9:I[6874,["6308","static/chunks/vendors-7237a82e-5b1f53a4b8780d18.js","563","static/chunks/vendors-ad6a2f20-97d1446c887ff15f.js","2662","static/chunks/vendors-04fef8b0-2eb254c773caab4c.js","8669","static/chunks/vendors-7ec938a2-dcd0e26d54d6db3d.js","8848","static/chunks/vendors-2ced652b-85aee2751f317897.js","622","static/chunks/vendors-b49fab05-31bf43a75248b59e.js","2432","static/chunks/vendors-27f02048-d5ca4f1bc0e362d3.js","408","static/chunks/vendors-fa70753b-237bdcd83400e2f4.js","4345","static/chunks/app/not-found-07c08cba74cb97f0.js"],""]
a:I[48031,["8888","static/chunks/supabase-55776fae-704411a5287a30d1.js","1459","static/chunks/supabase-0d08456b-7b71d5b8f6389e71.js","5738","static/chunks/utils-465036887cb9f2fe.js","9968","static/chunks/ui-components-b80f15b3-ba6d86bdab1b54cc.js","6060","static/chunks/ui-components-3acb5f41-600bd31c9d374c04.js","6308","static/chunks/vendors-7237a82e-5b1f53a4b8780d18.js","563","static/chunks/vendors-ad6a2f20-97d1446c887ff15f.js","2662","static/chunks/vendors-04fef8b0-2eb254c773caab4c.js","8669","static/chunks/vendors-7ec938a2-dcd0e26d54d6db3d.js","8848","static/chunks/vendors-2ced652b-85aee2751f317897.js","622","static/chunks/vendors-b49fab05-31bf43a75248b59e.js","2432","static/chunks/vendors-27f02048-d5ca4f1bc0e362d3.js","408","static/chunks/vendors-fa70753b-237bdcd83400e2f4.js","9558","static/chunks/app/layout-458df6b196458b9c.js"],"SpeedInsights"]
b:I[69243,["8888","static/chunks/supabase-55776fae-704411a5287a30d1.js","1459","static/chunks/supabase-0d08456b-7b71d5b8f6389e71.js","5738","static/chunks/utils-465036887cb9f2fe.js","9968","static/chunks/ui-components-b80f15b3-ba6d86bdab1b54cc.js","6060","static/chunks/ui-components-3acb5f41-600bd31c9d374c04.js","6308","static/chunks/vendors-7237a82e-5b1f53a4b8780d18.js","563","static/chunks/vendors-ad6a2f20-97d1446c887ff15f.js","2662","static/chunks/vendors-04fef8b0-2eb254c773caab4c.js","8669","static/chunks/vendors-7ec938a2-dcd0e26d54d6db3d.js","8848","static/chunks/vendors-2ced652b-85aee2751f317897.js","622","static/chunks/vendors-b49fab05-31bf43a75248b59e.js","2432","static/chunks/vendors-27f02048-d5ca4f1bc0e362d3.js","408","static/chunks/vendors-fa70753b-237bdcd83400e2f4.js","9558","static/chunks/app/layout-458df6b196458b9c.js"],""]
d:I[90894,[],"ClientPageRoot"]
e:I[79588,["7871","static/chunks/landing-components-3f5b97f1-9bf7127d768a248b.js","2115","static/chunks/landing-components-34b81939-3dbb3ff1852a0c84.js","8888","static/chunks/supabase-55776fae-704411a5287a30d1.js","1459","static/chunks/supabase-0d08456b-7b71d5b8f6389e71.js","5738","static/chunks/utils-465036887cb9f2fe.js","6308","static/chunks/vendors-7237a82e-5b1f53a4b8780d18.js","563","static/chunks/vendors-ad6a2f20-97d1446c887ff15f.js","2662","static/chunks/vendors-04fef8b0-2eb254c773caab4c.js","8669","static/chunks/vendors-7ec938a2-dcd0e26d54d6db3d.js","8848","static/chunks/vendors-2ced652b-85aee2751f317897.js","622","static/chunks/vendors-b49fab05-31bf43a75248b59e.js","2432","static/chunks/vendors-27f02048-d5ca4f1bc0e362d3.js","408","static/chunks/vendors-fa70753b-237bdcd83400e2f4.js","4680","static/chunks/app/auth/signin/page-94f39a913c9b48bd.js"],"default"]
11:I[59665,[],"OutletBoundary"]
14:I[74911,[],"AsyncMetadataOutlet"]
16:I[59665,[],"ViewportBoundary"]
18:I[59665,[],"MetadataBoundary"]
1a:I[26614,[],""]
:HL["/_next/static/css/5b576904c612405e.css","style"]
:HL["/_next/static/css/54d1e97f28a8bd00.css","style"]
:HL["/_next/static/css/a7273117cf35847d.css","style"]
c:T569,
            if ('serviceWorker' in navigator) {
              window.addEventListener('load', function() {
                navigator.serviceWorker.register('/sw.js')
                  .then(function(registration) {
                    console.log('✅ Service Worker registered successfully');

                    // Preload critical data after SW is ready
                    if (window.location.pathname === '/') {
                      // Preload landing page data
                      fetch('/api/system-status').catch(() => {});

                      // Prefetch all critical pages immediately
                      setTimeout(() => {
                        const criticalPages = ['/features', '/pricing', '/about', '/auth/signin', '/auth/signup'];
                        criticalPages.forEach(page => {
                          const link = document.createElement('link');
                          link.rel = 'prefetch';
                          link.href = page;
                          document.head.appendChild(link);
                        });
                      }, 500); // Much faster prefetching
                    }
                  })
                  .catch(function(registrationError) {
                    console.warn('⚠️ Service Worker registration failed:', registrationError);
                  });
              });
            }
          0:{"P":null,"b":"Ynh4vZIlB48nWqNFEGVkT","p":"","c":["","auth","signin"],"i":false,"f":[[["",{"children":["auth",{"children":["signin",{"children":["__PAGE__",{}]}]}]},"$undefined","$undefined",true],["",["$","$1","c",{"children":[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/5b576904c612405e.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}],["$","link","1",{"rel":"stylesheet","href":"/_next/static/css/54d1e97f28a8bd00.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}],["$","link","2",{"rel":"stylesheet","href":"/_next/static/css/a7273117cf35847d.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}]],["$","html",null,{"lang":"en","className":"__variable_e8ce0c","children":[["$","head",null,{"children":[["$","link",null,{"rel":"preload","href":"/api/custom-configs","as":"fetch","crossOrigin":"anonymous"}],["$","link",null,{"rel":"preload","href":"/api/system-status","as":"fetch","crossOrigin":"anonymous"}],["$","link",null,{"rel":"dns-prefetch","href":"//fonts.googleapis.com"}],["$","link",null,{"rel":"preconnect","href":"https://fonts.gstatic.com","crossOrigin":""}],["$","link",null,{"rel":"prefetch","href":"/dashboard"}],["$","link",null,{"rel":"prefetch","href":"/playground"}],["$","link",null,{"rel":"prefetch","href":"/logs"}],["$","link",null,{"rel":"prefetch","href":"/my-models"}]]}],["$","body",null,{"className":"font-sans antialiased","children":[["$","$2",null,{"fallback":null,"children":["$","$L3",null,{}]}],["$","$L4",null,{}],["$","$L5",null,{"enableUserBehaviorTracking":true,"enableNavigationTracking":true,"enableInteractionTracking":true}],["$","$L6",null,{"children":["$","$L7",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L8",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":[["$","div",null,{"className":"min-h-screen bg-black flex items-center justify-center","children":["$","div",null,{"className":"text-center","children":[["$","h1",null,{"className":"text-6xl font-bold text-orange-500 mb-4","children":"404"}],["$","h2",null,{"className":"text-2xl font-semibold text-white mb-4","children":"Page Not Found"}],["$","p",null,{"className":"text-gray-400 mb-8","children":"The page you're looking for doesn't exist."}],["$","$L9",null,{"href":"/","className":"bg-orange-500 hover:bg-orange-600 text-white px-6 py-3 rounded-lg transition-colors","children":"Go Home"}]]}]}],[]],"forbidden":"$undefined","unauthorized":"$undefined"}]}],["$","$La",null,{}],false,["$","$Lb",null,{"id":"sw-register","strategy":"afterInteractive","children":"$c"}]]}]]}]]}],{"children":["auth",["$","$1","c",{"children":[null,["$","$L7",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L8",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","forbidden":"$undefined","unauthorized":"$undefined"}]]}],{"children":["signin",["$","$1","c",{"children":[null,["$","$L7",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L8",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","forbidden":"$undefined","unauthorized":"$undefined"}]]}],{"children":["__PAGE__",["$","$1","c",{"children":[["$","$Ld",null,{"Component":"$e","searchParams":{},"params":{},"promises":["$@f","$@10"]}],null,["$","$L11",null,{"children":["$L12","$L13",["$","$L14",null,{"promise":"$@15"}]]}]]}],{},null,false]},null,false]},null,false]},null,false],["$","$1","h",{"children":[null,["$","$1","Nl2hABv2gBfdGUPpyYvQhv",{"children":[["$","$L16",null,{"children":"$L17"}],null]}],["$","$L18",null,{"children":"$L19"}]]}],false]],"m":"$undefined","G":["$1a","$undefined"],"s":false,"S":true}
1b:I[74911,[],"AsyncMetadata"]
f:{}
10:{}
19:["$","div",null,{"hidden":true,"children":["$","$2",null,{"fallback":null,"children":["$","$L1b",null,{"promise":"$@1c"}]}]}]
13:null
17:[["$","meta","0",{"charSet":"utf-8"}],["$","meta","1",{"name":"viewport","content":"width=device-width, initial-scale=1"}]]
12:null
15:{"metadata":[["$","title","0",{"children":"RouKey - Smart LLM Key Router"}],["$","meta","1",{"name":"description","content":"Advanced LLM API key routing and management"}],["$","link","2",{"rel":"icon","href":"/favicon.ico","type":"image/x-icon","sizes":"16x16"}]],"error":null,"digest":"$undefined"}
1c:{"metadata":"$15:metadata","error":null,"digest":"$undefined"}
