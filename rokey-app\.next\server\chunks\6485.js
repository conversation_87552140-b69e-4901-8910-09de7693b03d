exports.id=6485,exports.ids=[6485],exports.modules={32801:(e,t,r)=>{"use strict";r.d(t,{F:()=>a});var s=r(56534);class a{static{this.KEY_PREFIX="rk_live_"}static{this.RANDOM_PART_LENGTH=8}static{this.SECRET_PART_LENGTH=32}static async generateApiKey(){let e=Array.from(crypto.getRandomValues(new Uint8Array(this.RANDOM_PART_LENGTH/2)),e=>e.toString(16).padStart(2,"0")).join(""),t=this.generateRandomString(this.SECRET_PART_LENGTH),r=`${this.KEY_PREFIX}${e}`,s=`${r}_${t}`,a=await this.hashApiKey(s);return{fullKey:s,prefix:r,secretPart:t,hash:a}}static generateRandomString(e){let t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",r="";for(let s=0;s<e;s++){let e=crypto.getRandomValues(new Uint8Array(1))[0]%t.length;r+=t[e]}return r}static async hashApiKey(e){let t=new TextEncoder().encode(e);return Array.from(new Uint8Array(await crypto.subtle.digest("SHA-256",t)),e=>e.toString(16).padStart(2,"0")).join("")}static isValidFormat(e){return RegExp(`^${this.KEY_PREFIX}[a-f0-9]{${this.RANDOM_PART_LENGTH}}_[a-zA-Z0-9]{${this.SECRET_PART_LENGTH}}$`).test(e)}static extractPrefix(e){let t=e.split("_");return t.length>=3?`${t[0]}_${t[1]}_${t[2]}`:""}static async encryptSuffix(e){let t=e.slice(-4);return await (0,s.w)(t)}static async decryptSuffix(e){try{return await (0,s.Y)(e)}catch(e){return"xxxx"}}static async createMaskedKey(e,t){let r=await this.decryptSuffix(t),s=this.SECRET_PART_LENGTH-4;return`${e}_${"*".repeat(s)}${r}`}static validateSubscriptionLimits(e,t){let r={starter:5,professional:25,enterprise:100},s=r[e]||r.starter;return t>=s?{allowed:!1,limit:s,message:`You have reached the maximum number of API keys (${s}) for your ${e} plan.`}:{allowed:!0,limit:s}}}},39727:()=>{},47990:()=>{},49859:(e,t,r)=>{"use strict";r.d(t,{S:()=>n});var s=r(39398),a=r(32801);class i{constructor(){this.supabase=(0,s.createClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co",process.env.SUPABASE_SERVICE_ROLE_KEY)}async validateApiKey(e,t){try{if(!a.F.isValidFormat(e))return{isValid:!1,error:"Invalid API key format"};let r=await a.F.hashApiKey(e),{data:s,error:i}=await this.supabase.from("user_generated_api_keys").select(`
          *,
          custom_api_configs!inner(
            id,
            name,
            user_id,
            routing_strategy,
            routing_strategy_params
          )
        `).eq("key_hash",r).eq("status","active").single();if(i||!s)return{isValid:!1,error:"Invalid or inactive API key"};if(s.expires_at&&new Date(s.expires_at)<new Date)return await this.supabase.from("user_generated_api_keys").update({status:"expired"}).eq("id",s.id),{isValid:!1,error:"API key has expired"};if(s.allowed_ips&&s.allowed_ips.length>0&&t&&!this.checkIpAllowed(t,s.allowed_ips))return{isValid:!1,error:"IP address not allowed for this API key"};return await this.updateLastUsed(s.id,t),{isValid:!0,apiKey:s}}catch(e){return{isValid:!1,error:"Internal server error during validation"}}}checkIpAllowed(e,t){return t.includes(e)||t.includes("*")}async updateLastUsed(e,t){let{data:r}=await this.supabase.from("user_generated_api_keys").select("total_requests").eq("id",e).single(),s={last_used_at:new Date().toISOString(),total_requests:(r?.total_requests||0)+1};t&&(s.last_used_ip=t),await this.supabase.from("user_generated_api_keys").update(s).eq("id",e)}async logApiUsage(e,t,r,s){await this.supabase.from("user_api_key_usage_logs").insert({user_generated_api_key_id:e,user_id:t,custom_api_config_id:r,endpoint:s.endpoint,http_method:s.method,status_code:s.statusCode,ip_address:s.ipAddress,user_agent:s.userAgent,referer:s.referer,model_used:s.modelUsed,provider_used:s.providerUsed,tokens_prompt:s.tokensPrompt,tokens_completion:s.tokensCompletion,cost_usd:s.costUsd,response_time_ms:s.responseTimeMs,error_message:s.errorMessage,error_type:s.errorType})}}class n{constructor(){this.validator=new i}extractApiKey(e){let t=e.headers.get("authorization");if(t){let e=t.match(/^Bearer\s+(.+)$/i);if(e)return e[1]}let r=e.headers.get("x-api-key");return r||null}getClientIp(e){let t=e.headers.get("x-forwarded-for");if(t)return t.split(",")[0].trim();let r=e.headers.get("x-real-ip");if(r)return r;let s=e.headers.get("cf-connecting-ip");return s||"127.0.0.1"}async authenticateRequest(e){try{let t=this.extractApiKey(e);if(!t)return{success:!1,error:'API key is required. Provide it in Authorization header as "Bearer YOUR_API_KEY" or in x-api-key header.',statusCode:401};let r=this.getClientIp(e),s=await this.validator.validateApiKey(t,r);if(!s.isValid){let e=401;return s.error?.includes("expired")?e=401:s.error?.includes("IP address not allowed")&&(e=403),{success:!1,error:s.error||"Invalid API key",statusCode:e}}return{success:!0,userApiKey:s.apiKey,userConfig:s.apiKey.custom_api_configs,ipAddress:r}}catch(e){return{success:!1,error:"Internal authentication error",statusCode:500}}}async logApiUsage(e,t,r,s){try{let a=new URL(t.url);await this.validator.logApiUsage(e.id,e.user_id,e.custom_api_config_id,{endpoint:a.pathname,method:t.method,statusCode:r.statusCode,ipAddress:s,userAgent:t.headers.get("user-agent")||void 0,referer:t.headers.get("referer")||void 0,modelUsed:r.modelUsed,providerUsed:r.providerUsed,tokensPrompt:r.tokensPrompt,tokensCompletion:r.tokensCompletion,costUsd:r.costUsd,responseTimeMs:r.responseTimeMs,errorMessage:r.errorMessage,errorType:r.errorType})}catch(e){}}hasPermission(e,t){let r=e.permissions;switch(t){case"chat":return!0===r.chat;case"streaming":return!0===r.streaming;case"all_models":return!0===r.all_models;default:return!1}}isOriginAllowed(e,t){if(!t)return!0;let r=e.allowed_domains;return!r||0===r.length||r.some(e=>{if("*"===e)return!0;if(e.startsWith("*.")){let r=e.slice(2);return t.endsWith(r)}return t===e||t===`https://${e}`||t===`http://${e}`})}createErrorResponse(e,t){return new Response(JSON.stringify({error:{message:e,type:this.getErrorType(t),code:t}}),{status:t,headers:{"Content-Type":"application/json"}})}getErrorType(e){switch(e){case 401:return"authentication_error";case 403:return"permission_denied";case 500:return"internal_error";default:return"api_error"}}}},51906:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=51906,e.exports=t},56534:(e,t,r)=>{"use strict";r.d(t,{Y:()=>l,w:()=>d});let s="AES-GCM",a=process.env.ROKEY_ENCRYPTION_KEY;if(!a||64!==a.length)throw Error("Invalid ROKEY_ENCRYPTION_KEY. Please set a 64-character hex string (32 bytes) for ROKEY_ENCRYPTION_KEY in your .env.local file.");function i(e){let t=new Uint8Array(e.length/2);for(let r=0;r<e.length;r+=2)t[r/2]=parseInt(e.substr(r,2),16);return t}function n(e){return Array.from(e,e=>e.toString(16).padStart(2,"0")).join("")}let o=i(a);async function d(e){if("string"!=typeof e||0===e.length)throw Error("Encryption input must be a non-empty string.");let t=crypto.getRandomValues(new Uint8Array(12)),r=await crypto.subtle.importKey("raw",o,{name:s},!1,["encrypt"]),a=new TextEncoder().encode(e),i=new Uint8Array(await crypto.subtle.encrypt({name:s,iv:t},r,a)),d=i.slice(0,-16),l=i.slice(-16);return`${n(t)}:${n(l)}:${n(d)}`}async function l(e){if("string"!=typeof e||0===e.length)throw Error("Decryption input must be a non-empty string.");let t=e.split(":");if(3!==t.length)throw Error("Invalid encrypted text format. Expected iv:authTag:encryptedData");let r=i(t[0]),a=i(t[1]),n=i(t[2]);if(12!==r.length)throw Error("Invalid IV length. Expected 12 bytes.");if(16!==a.length)throw Error("Invalid authTag length. Expected 16 bytes.");let d=await crypto.subtle.importKey("raw",o,{name:s},!1,["decrypt"]),l=new Uint8Array(n.length+a.length);l.set(n),l.set(a,n.length);let u=await crypto.subtle.decrypt({name:s,iv:r},d,l);return new TextDecoder().decode(u)}},78335:()=>{},96487:()=>{}};