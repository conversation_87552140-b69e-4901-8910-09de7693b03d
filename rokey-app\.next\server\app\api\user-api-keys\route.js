(()=>{var e={};e.id=1453,e.ids=[1453,1489],e.modules={2507:(e,t,r)=>{"use strict";r.d(t,{Q:()=>n,createSupabaseServerClientOnRequest:()=>a});var s=r(34386),i=r(44999);async function a(){let e=await (0,i.UL)();return(0,s.createServerClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:t=>e.get(t)?.value,set(t,r,s){try{e.set({name:t,value:r,...s})}catch(e){}},remove(t,r){try{e.set({name:t,value:"",...r})}catch(e){}}}})}function n(e){return(0,s.createServerClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:t=>e.cookies.get(t)?.value,set(e,t,r){},remove(e,t){}}})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32801:(e,t,r)=>{"use strict";r.d(t,{F:()=>i});var s=r(56534);class i{static{this.KEY_PREFIX="rk_live_"}static{this.RANDOM_PART_LENGTH=8}static{this.SECRET_PART_LENGTH=32}static async generateApiKey(){let e=Array.from(crypto.getRandomValues(new Uint8Array(this.RANDOM_PART_LENGTH/2)),e=>e.toString(16).padStart(2,"0")).join(""),t=this.generateRandomString(this.SECRET_PART_LENGTH),r=`${this.KEY_PREFIX}${e}`,s=`${r}_${t}`,i=await this.hashApiKey(s);return{fullKey:s,prefix:r,secretPart:t,hash:i}}static generateRandomString(e){let t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",r="";for(let s=0;s<e;s++){let e=crypto.getRandomValues(new Uint8Array(1))[0]%t.length;r+=t[e]}return r}static async hashApiKey(e){let t=new TextEncoder().encode(e);return Array.from(new Uint8Array(await crypto.subtle.digest("SHA-256",t)),e=>e.toString(16).padStart(2,"0")).join("")}static isValidFormat(e){return RegExp(`^${this.KEY_PREFIX}[a-f0-9]{${this.RANDOM_PART_LENGTH}}_[a-zA-Z0-9]{${this.SECRET_PART_LENGTH}}$`).test(e)}static extractPrefix(e){let t=e.split("_");return t.length>=3?`${t[0]}_${t[1]}_${t[2]}`:""}static async encryptSuffix(e){let t=e.slice(-4);return await (0,s.w)(t)}static async decryptSuffix(e){try{return await (0,s.Y)(e)}catch(e){return"xxxx"}}static async createMaskedKey(e,t){let r=await this.decryptSuffix(t),s=this.SECRET_PART_LENGTH-4;return`${e}_${"*".repeat(s)}${r}`}static validateSubscriptionLimits(e,t){let r={starter:5,professional:25,enterprise:100},s=r[e]||r.starter;return t>=s?{allowed:!1,limit:s,message:`You have reached the maximum number of API keys (${s}) for your ${e} plan.`}:{allowed:!0,limit:s}}}},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51906:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=51906,e.exports=t},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56534:(e,t,r)=>{"use strict";r.d(t,{Y:()=>c,w:()=>u});let s="AES-GCM",i=process.env.ROKEY_ENCRYPTION_KEY;if(!i||64!==i.length)throw Error("Invalid ROKEY_ENCRYPTION_KEY. Please set a 64-character hex string (32 bytes) for ROKEY_ENCRYPTION_KEY in your .env.local file.");function a(e){let t=new Uint8Array(e.length/2);for(let r=0;r<e.length;r+=2)t[r/2]=parseInt(e.substr(r,2),16);return t}function n(e){return Array.from(e,e=>e.toString(16).padStart(2,"0")).join("")}let o=a(i);async function u(e){if("string"!=typeof e||0===e.length)throw Error("Encryption input must be a non-empty string.");let t=crypto.getRandomValues(new Uint8Array(12)),r=await crypto.subtle.importKey("raw",o,{name:s},!1,["encrypt"]),i=new TextEncoder().encode(e),a=new Uint8Array(await crypto.subtle.encrypt({name:s,iv:t},r,i)),u=a.slice(0,-16),c=a.slice(-16);return`${n(t)}:${n(c)}:${n(u)}`}async function c(e){if("string"!=typeof e||0===e.length)throw Error("Decryption input must be a non-empty string.");let t=e.split(":");if(3!==t.length)throw Error("Invalid encrypted text format. Expected iv:authTag:encryptedData");let r=a(t[0]),i=a(t[1]),n=a(t[2]);if(12!==r.length)throw Error("Invalid IV length. Expected 12 bytes.");if(16!==i.length)throw Error("Invalid authTag length. Expected 16 bytes.");let u=await crypto.subtle.importKey("raw",o,{name:s},!1,["decrypt"]),c=new Uint8Array(n.length+i.length);c.set(n),c.set(i,n.length);let p=await crypto.subtle.decrypt({name:s,iv:r},u,c);return new TextDecoder().decode(p)}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},81876:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>g,routeModule:()=>_,serverHooks:()=>h,workAsyncStorage:()=>f,workUnitAsyncStorage:()=>m});var s={};r.r(s),r.d(s,{GET:()=>y,POST:()=>l});var i=r(96559),a=r(48088),n=r(37719),o=r(32190),u=r(2507),c=r(32801),p=r(45697);let d=p.z.object({custom_api_config_id:p.z.string().uuid(),key_name:p.z.string().min(1).max(100),expires_at:p.z.string().datetime().optional()});async function l(e){let t=(0,u.Q)(e);try{let{data:{user:r},error:s}=await t.auth.getUser();if(s||!r)return o.NextResponse.json({error:"Unauthorized"},{status:401});let i=await e.json(),a=d.parse(i),{data:n,error:u}=await t.from("custom_api_configs").select("id, name, user_id").eq("id",a.custom_api_config_id).eq("user_id",r.id).single();if(u||!n)return o.NextResponse.json({error:"Custom API configuration not found or access denied"},{status:404});let{data:p}=await t.from("subscriptions").select("tier").eq("user_id",r.id).eq("status","active").single(),l=p?.tier||"starter",{count:y}=await t.from("user_generated_api_keys").select("*",{count:"exact",head:!0}).eq("user_id",r.id).eq("status","active"),_=c.F.validateSubscriptionLimits(l,y||0);if(!_.allowed)return o.NextResponse.json({error:_.message},{status:403});let{data:f}=await t.from("user_generated_api_keys").select("id").eq("custom_api_config_id",a.custom_api_config_id).eq("key_name",a.key_name).eq("status","active").single();if(f)return o.NextResponse.json({error:"An API key with this name already exists for this configuration"},{status:409});let{fullKey:m,prefix:h,secretPart:g,hash:x}=await c.F.generateApiKey(),w=await c.F.encryptSuffix(g),R={user_id:r.id,custom_api_config_id:a.custom_api_config_id,key_name:a.key_name,key_prefix:h,key_hash:x,encrypted_key_suffix:w,permissions:{chat:!0,streaming:!0,all_models:!0},allowed_ips:[],allowed_domains:[],expires_at:a.expires_at||null},{data:k,error:E}=await t.from("user_generated_api_keys").insert(R).select().single();if(E)return o.NextResponse.json({error:"Failed to create API key"},{status:500});let I={id:k.id,key_name:k.key_name,api_key:m,key_prefix:k.key_prefix,permissions:k.permissions,created_at:k.created_at,expires_at:k.expires_at};return o.NextResponse.json(I,{status:201})}catch(e){if(e instanceof p.z.ZodError)return o.NextResponse.json({error:"Invalid request data",details:e.errors},{status:400});return o.NextResponse.json({error:"Internal server error"},{status:500})}}async function y(e){let t=(0,u.Q)(e);try{let{data:{user:r},error:s}=await t.auth.getUser();if(s||!r)return o.NextResponse.json({error:"Unauthorized"},{status:401});let{searchParams:i}=new URL(e.url),a=i.get("config_id"),n=t.from("user_generated_api_keys").select(`
        id,
        key_name,
        key_prefix,
        encrypted_key_suffix,
        permissions,
        allowed_ips,
        allowed_domains,
        total_requests,
        last_used_at,
        status,
        expires_at,
        created_at,
        custom_api_configs!inner(
          id,
          name
        )
      `).eq("user_id",r.id).order("created_at",{ascending:!1});a&&(n=n.eq("custom_api_config_id",a));let{data:u,error:p}=await n;if(p)return o.NextResponse.json({error:"Failed to fetch API keys"},{status:500});let d=u.map(e=>({...e,masked_key:c.F.createMaskedKey(e.key_prefix,e.encrypted_key_suffix),encrypted_key_suffix:void 0}));return o.NextResponse.json({api_keys:d})}catch(e){return o.NextResponse.json({error:"Internal server error"},{status:500})}}let _=new i.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/user-api-keys/route",pathname:"/api/user-api-keys",filename:"route",bundlePath:"app/api/user-api-keys/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\user-api-keys\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:f,workUnitAsyncStorage:m,serverHooks:h}=_;function g(){return(0,n.patchFetch)({workAsyncStorage:f,workUnitAsyncStorage:m})}},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580,9398,3410,5697],()=>r(81876));module.exports=s})();