"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("middleware",{

/***/ "(middleware)/./node_modules/next/dist/esm/server/after/after-context.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/esm/server/after/after-context.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AfterContext: () => (/* binding */ AfterContext)\n/* harmony export */ });\n/* harmony import */ var next_dist_compiled_p_queue__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/compiled/p-queue */ \"(middleware)/./node_modules/next/dist/compiled/p-queue/index.js\");\n/* harmony import */ var next_dist_compiled_p_queue__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_compiled_p_queue__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _shared_lib_invariant_error__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../shared/lib/invariant-error */ \"(middleware)/./node_modules/next/dist/esm/shared/lib/invariant-error.js\");\n/* harmony import */ var _shared_lib_is_thenable__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../shared/lib/is-thenable */ \"(middleware)/./node_modules/next/dist/esm/shared/lib/is-thenable.js\");\n/* harmony import */ var _app_render_work_async_storage_external__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../app-render/work-async-storage.external */ \"(shared)/./node_modules/next/dist/esm/server/app-render/work-async-storage.external.js?73ab\");\n/* harmony import */ var _revalidation_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../revalidation-utils */ \"(middleware)/./node_modules/next/dist/esm/server/revalidation-utils.js\");\n/* harmony import */ var _app_render_async_local_storage__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../app-render/async-local-storage */ \"(middleware)/./node_modules/next/dist/esm/server/app-render/async-local-storage.js\");\n/* harmony import */ var _app_render_work_unit_async_storage_external__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../app-render/work-unit-async-storage.external */ \"(shared)/./node_modules/next/dist/esm/server/app-render/work-unit-async-storage.external.js?418e\");\n/* harmony import */ var _app_render_after_task_async_storage_external__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../app-render/after-task-async-storage.external */ \"(middleware)/./node_modules/next/dist/esm/server/app-render/after-task-async-storage.external.js\");\n\n\n\n\n\n\n\n\nclass AfterContext {\n    constructor({ waitUntil, onClose, onTaskError }){\n        this.workUnitStores = new Set();\n        this.waitUntil = waitUntil;\n        this.onClose = onClose;\n        this.onTaskError = onTaskError;\n        this.callbackQueue = new (next_dist_compiled_p_queue__WEBPACK_IMPORTED_MODULE_0___default())();\n        this.callbackQueue.pause();\n    }\n    after(task) {\n        if ((0,_shared_lib_is_thenable__WEBPACK_IMPORTED_MODULE_2__.isThenable)(task)) {\n            if (!this.waitUntil) {\n                errorWaitUntilNotAvailable();\n            }\n            this.waitUntil(task.catch((error)=>this.reportTaskError('promise', error)));\n        } else if (typeof task === 'function') {\n            // TODO(after): implement tracing\n            this.addCallback(task);\n        } else {\n            throw Object.defineProperty(new Error('`after()`: Argument must be a promise or a function'), \"__NEXT_ERROR_CODE\", {\n                value: \"E50\",\n                enumerable: false,\n                configurable: true\n            });\n        }\n    }\n    addCallback(callback) {\n        // if something is wrong, throw synchronously, bubbling up to the `after` callsite.\n        if (!this.waitUntil) {\n            errorWaitUntilNotAvailable();\n        }\n        const workUnitStore = _app_render_work_unit_async_storage_external__WEBPACK_IMPORTED_MODULE_6__.workUnitAsyncStorage.getStore();\n        if (workUnitStore) {\n            this.workUnitStores.add(workUnitStore);\n        }\n        const afterTaskStore = _app_render_after_task_async_storage_external__WEBPACK_IMPORTED_MODULE_7__.afterTaskAsyncStorage.getStore();\n        // This is used for checking if request APIs can be called inside `after`.\n        // Note that we need to check the phase in which the *topmost* `after` was called (which should be \"action\"),\n        // not the current phase (which might be \"after\" if we're in a nested after).\n        // Otherwise, we might allow `after(() => headers())`, but not `after(() => after(() => headers()))`.\n        const rootTaskSpawnPhase = afterTaskStore ? afterTaskStore.rootTaskSpawnPhase // nested after\n         : workUnitStore == null ? void 0 : workUnitStore.phase // topmost after\n        ;\n        // this should only happen once.\n        if (!this.runCallbacksOnClosePromise) {\n            this.runCallbacksOnClosePromise = this.runCallbacksOnClose();\n            this.waitUntil(this.runCallbacksOnClosePromise);\n        }\n        // Bind the callback to the current execution context (i.e. preserve all currently available ALS-es).\n        // We do this because we want all of these to be equivalent in every regard except timing:\n        //   after(() => x())\n        //   after(x())\n        //   await x()\n        const wrappedCallback = (0,_app_render_async_local_storage__WEBPACK_IMPORTED_MODULE_5__.bindSnapshot)(async ()=>{\n            try {\n                await _app_render_after_task_async_storage_external__WEBPACK_IMPORTED_MODULE_7__.afterTaskAsyncStorage.run({\n                    rootTaskSpawnPhase\n                }, ()=>callback());\n            } catch (error) {\n                this.reportTaskError('function', error);\n            }\n        });\n        this.callbackQueue.add(wrappedCallback);\n    }\n    async runCallbacksOnClose() {\n        await new Promise((resolve)=>this.onClose(resolve));\n        return this.runCallbacks();\n    }\n    async runCallbacks() {\n        if (this.callbackQueue.size === 0) return;\n        for (const workUnitStore of this.workUnitStores){\n            workUnitStore.phase = 'after';\n        }\n        const workStore = _app_render_work_async_storage_external__WEBPACK_IMPORTED_MODULE_3__.workAsyncStorage.getStore();\n        if (!workStore) {\n            throw Object.defineProperty(new _shared_lib_invariant_error__WEBPACK_IMPORTED_MODULE_1__.InvariantError('Missing workStore in AfterContext.runCallbacks'), \"__NEXT_ERROR_CODE\", {\n                value: \"E547\",\n                enumerable: false,\n                configurable: true\n            });\n        }\n        return (0,_revalidation_utils__WEBPACK_IMPORTED_MODULE_4__.withExecuteRevalidates)(workStore, ()=>{\n            this.callbackQueue.start();\n            return this.callbackQueue.onIdle();\n        });\n    }\n    reportTaskError(taskKind, error) {\n        // TODO(after): this is fine for now, but will need better intergration with our error reporting.\n        // TODO(after): should we log this if we have a onTaskError callback?\n        console.error(taskKind === 'promise' ? `A promise passed to \\`after()\\` rejected:` : `An error occurred in a function passed to \\`after()\\`:`, error);\n        if (this.onTaskError) {\n            // this is very defensive, but we really don't want anything to blow up in an error handler\n            try {\n                this.onTaskError == null ? void 0 : this.onTaskError.call(this, error);\n            } catch (handlerError) {\n                console.error(Object.defineProperty(new _shared_lib_invariant_error__WEBPACK_IMPORTED_MODULE_1__.InvariantError('`onTaskError` threw while handling an error thrown from an `after` task', {\n                    cause: handlerError\n                }), \"__NEXT_ERROR_CODE\", {\n                    value: \"E569\",\n                    enumerable: false,\n                    configurable: true\n                }));\n            }\n        }\n    }\n}\nfunction errorWaitUntilNotAvailable() {\n    throw Object.defineProperty(new Error('`after()` will not work correctly, because `waitUntil` is not available in the current environment.'), \"__NEXT_ERROR_CODE\", {\n        value: \"E91\",\n        enumerable: false,\n        configurable: true\n    });\n}\n\n//# sourceMappingURL=after-context.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./node_modules/next/dist/esm/server/after/after-context.js\n");

/***/ }),

/***/ "(middleware)/./node_modules/next/dist/esm/server/after/after.js":
/*!**********************************************************!*\
  !*** ./node_modules/next/dist/esm/server/after/after.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   after: () => (/* binding */ after)\n/* harmony export */ });\n/* harmony import */ var _app_render_work_async_storage_external__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../app-render/work-async-storage.external */ \"(shared)/./node_modules/next/dist/esm/server/app-render/work-async-storage.external.js?73ab\");\n\n/**\n * This function allows you to schedule callbacks to be executed after the current request finishes.\n */ function after(task) {\n    const workStore = _app_render_work_async_storage_external__WEBPACK_IMPORTED_MODULE_0__.workAsyncStorage.getStore();\n    if (!workStore) {\n        // TODO(after): the linked docs page talks about *dynamic* APIs, which after soon won't be anymore\n        throw Object.defineProperty(new Error('`after` was called outside a request scope. Read more: https://nextjs.org/docs/messages/next-dynamic-api-wrong-context'), \"__NEXT_ERROR_CODE\", {\n            value: \"E468\",\n            enumerable: false,\n            configurable: true\n        });\n    }\n    const { afterContext } = workStore;\n    return afterContext.after(task);\n}\n\n//# sourceMappingURL=after.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKG1pZGRsZXdhcmUpLy4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9lc20vc2VydmVyL2FmdGVyL2FmdGVyLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQTZFO0FBQzdFO0FBQ0E7QUFDQSxJQUFXO0FBQ1gsc0JBQXNCLHFGQUFnQjtBQUN0QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQSxZQUFZLGVBQWU7QUFDM0I7QUFDQTs7QUFFQSIsInNvdXJjZXMiOlsiQzpcXFJvS2V5IEFwcFxccm9rZXktYXBwXFxub2RlX21vZHVsZXNcXG5leHRcXGRpc3RcXGVzbVxcc2VydmVyXFxhZnRlclxcYWZ0ZXIuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgd29ya0FzeW5jU3RvcmFnZSB9IGZyb20gJy4uL2FwcC1yZW5kZXIvd29yay1hc3luYy1zdG9yYWdlLmV4dGVybmFsJztcbi8qKlxuICogVGhpcyBmdW5jdGlvbiBhbGxvd3MgeW91IHRvIHNjaGVkdWxlIGNhbGxiYWNrcyB0byBiZSBleGVjdXRlZCBhZnRlciB0aGUgY3VycmVudCByZXF1ZXN0IGZpbmlzaGVzLlxuICovIGV4cG9ydCBmdW5jdGlvbiBhZnRlcih0YXNrKSB7XG4gICAgY29uc3Qgd29ya1N0b3JlID0gd29ya0FzeW5jU3RvcmFnZS5nZXRTdG9yZSgpO1xuICAgIGlmICghd29ya1N0b3JlKSB7XG4gICAgICAgIC8vIFRPRE8oYWZ0ZXIpOiB0aGUgbGlua2VkIGRvY3MgcGFnZSB0YWxrcyBhYm91dCAqZHluYW1pYyogQVBJcywgd2hpY2ggYWZ0ZXIgc29vbiB3b24ndCBiZSBhbnltb3JlXG4gICAgICAgIHRocm93IE9iamVjdC5kZWZpbmVQcm9wZXJ0eShuZXcgRXJyb3IoJ2BhZnRlcmAgd2FzIGNhbGxlZCBvdXRzaWRlIGEgcmVxdWVzdCBzY29wZS4gUmVhZCBtb3JlOiBodHRwczovL25leHRqcy5vcmcvZG9jcy9tZXNzYWdlcy9uZXh0LWR5bmFtaWMtYXBpLXdyb25nLWNvbnRleHQnKSwgXCJfX05FWFRfRVJST1JfQ09ERVwiLCB7XG4gICAgICAgICAgICB2YWx1ZTogXCJFNDY4XCIsXG4gICAgICAgICAgICBlbnVtZXJhYmxlOiBmYWxzZSxcbiAgICAgICAgICAgIGNvbmZpZ3VyYWJsZTogdHJ1ZVxuICAgICAgICB9KTtcbiAgICB9XG4gICAgY29uc3QgeyBhZnRlckNvbnRleHQgfSA9IHdvcmtTdG9yZTtcbiAgICByZXR1cm4gYWZ0ZXJDb250ZXh0LmFmdGVyKHRhc2spO1xufVxuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1hZnRlci5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(middleware)/./node_modules/next/dist/esm/server/after/after.js\n");

/***/ }),

/***/ "(middleware)/./node_modules/next/dist/esm/server/app-render/dynamic-rendering.js":
/*!***************************************************************************!*\
  !*** ./node_modules/next/dist/esm/server/app-render/dynamic-rendering.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Postpone: () => (/* binding */ Postpone),\n/* harmony export */   abortAndThrowOnSynchronousRequestDataAccess: () => (/* binding */ abortAndThrowOnSynchronousRequestDataAccess),\n/* harmony export */   abortOnSynchronousPlatformIOAccess: () => (/* binding */ abortOnSynchronousPlatformIOAccess),\n/* harmony export */   accessedDynamicData: () => (/* binding */ accessedDynamicData),\n/* harmony export */   annotateDynamicAccess: () => (/* binding */ annotateDynamicAccess),\n/* harmony export */   consumeDynamicAccess: () => (/* binding */ consumeDynamicAccess),\n/* harmony export */   createDynamicTrackingState: () => (/* binding */ createDynamicTrackingState),\n/* harmony export */   createDynamicValidationState: () => (/* binding */ createDynamicValidationState),\n/* harmony export */   createHangingInputAbortSignal: () => (/* binding */ createHangingInputAbortSignal),\n/* harmony export */   createPostponedAbortSignal: () => (/* binding */ createPostponedAbortSignal),\n/* harmony export */   formatDynamicAPIAccesses: () => (/* binding */ formatDynamicAPIAccesses),\n/* harmony export */   getFirstDynamicReason: () => (/* binding */ getFirstDynamicReason),\n/* harmony export */   isDynamicPostpone: () => (/* binding */ isDynamicPostpone),\n/* harmony export */   isPrerenderInterruptedError: () => (/* binding */ isPrerenderInterruptedError),\n/* harmony export */   markCurrentScopeAsDynamic: () => (/* binding */ markCurrentScopeAsDynamic),\n/* harmony export */   postponeWithTracking: () => (/* binding */ postponeWithTracking),\n/* harmony export */   throwIfDisallowedDynamic: () => (/* binding */ throwIfDisallowedDynamic),\n/* harmony export */   throwToInterruptStaticGeneration: () => (/* binding */ throwToInterruptStaticGeneration),\n/* harmony export */   trackAllowedDynamicAccess: () => (/* binding */ trackAllowedDynamicAccess),\n/* harmony export */   trackDynamicDataInDynamicRender: () => (/* binding */ trackDynamicDataInDynamicRender),\n/* harmony export */   trackFallbackParamAccessed: () => (/* binding */ trackFallbackParamAccessed),\n/* harmony export */   trackSynchronousPlatformIOAccessInDev: () => (/* binding */ trackSynchronousPlatformIOAccessInDev),\n/* harmony export */   trackSynchronousRequestDataAccessInDev: () => (/* binding */ trackSynchronousRequestDataAccessInDev),\n/* harmony export */   useDynamicRouteParams: () => (/* binding */ useDynamicRouteParams)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(middleware)/./node_modules/next/dist/compiled/react/react.react-server.js\");\n/* harmony import */ var _client_components_hooks_server_context__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../client/components/hooks-server-context */ \"(middleware)/./node_modules/next/dist/esm/client/components/hooks-server-context.js\");\n/* harmony import */ var _client_components_static_generation_bailout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../client/components/static-generation-bailout */ \"(middleware)/./node_modules/next/dist/esm/client/components/static-generation-bailout.js\");\n/* harmony import */ var _work_unit_async_storage_external__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./work-unit-async-storage.external */ \"(shared)/./node_modules/next/dist/esm/server/app-render/work-unit-async-storage.external.js?418e\");\n/* harmony import */ var _app_render_work_async_storage_external__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../app-render/work-async-storage.external */ \"(shared)/./node_modules/next/dist/esm/server/app-render/work-async-storage.external.js?73ab\");\n/* harmony import */ var _dynamic_rendering_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../dynamic-rendering-utils */ \"(middleware)/./node_modules/next/dist/esm/server/dynamic-rendering-utils.js\");\n/* harmony import */ var _lib_metadata_metadata_constants__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../lib/metadata/metadata-constants */ \"(middleware)/./node_modules/next/dist/esm/lib/metadata/metadata-constants.js\");\n/* harmony import */ var _lib_scheduler__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../lib/scheduler */ \"(middleware)/./node_modules/next/dist/esm/lib/scheduler.js\");\n/**\n * The functions provided by this module are used to communicate certain properties\n * about the currently running code so that Next.js can make decisions on how to handle\n * the current execution in different rendering modes such as pre-rendering, resuming, and SSR.\n *\n * Today Next.js treats all code as potentially static. Certain APIs may only make sense when dynamically rendering.\n * Traditionally this meant deopting the entire render to dynamic however with PPR we can now deopt parts\n * of a React tree as dynamic while still keeping other parts static. There are really two different kinds of\n * Dynamic indications.\n *\n * The first is simply an intention to be dynamic. unstable_noStore is an example of this where\n * the currently executing code simply declares that the current scope is dynamic but if you use it\n * inside unstable_cache it can still be cached. This type of indication can be removed if we ever\n * make the default dynamic to begin with because the only way you would ever be static is inside\n * a cache scope which this indication does not affect.\n *\n * The second is an indication that a dynamic data source was read. This is a stronger form of dynamic\n * because it means that it is inappropriate to cache this at all. using a dynamic data source inside\n * unstable_cache should error. If you want to use some dynamic data inside unstable_cache you should\n * read that data outside the cache and pass it in as an argument to the cached function.\n */ // Once postpone is in stable we should switch to importing the postpone export directly\n\n\n\n\n\n\n\n\nconst hasPostpone = typeof react__WEBPACK_IMPORTED_MODULE_0__.unstable_postpone === 'function';\nfunction createDynamicTrackingState(isDebugDynamicAccesses) {\n    return {\n        isDebugDynamicAccesses,\n        dynamicAccesses: [],\n        syncDynamicExpression: undefined,\n        syncDynamicErrorWithStack: null\n    };\n}\nfunction createDynamicValidationState() {\n    return {\n        hasSuspendedDynamic: false,\n        hasDynamicMetadata: false,\n        hasDynamicViewport: false,\n        hasSyncDynamicErrors: false,\n        dynamicErrors: []\n    };\n}\nfunction getFirstDynamicReason(trackingState) {\n    var _trackingState_dynamicAccesses_;\n    return (_trackingState_dynamicAccesses_ = trackingState.dynamicAccesses[0]) == null ? void 0 : _trackingState_dynamicAccesses_.expression;\n}\n/**\n * This function communicates that the current scope should be treated as dynamic.\n *\n * In most cases this function is a no-op but if called during\n * a PPR prerender it will postpone the current sub-tree and calling\n * it during a normal prerender will cause the entire prerender to abort\n */ function markCurrentScopeAsDynamic(store, workUnitStore, expression) {\n    if (workUnitStore) {\n        if (workUnitStore.type === 'cache' || workUnitStore.type === 'unstable-cache') {\n            // inside cache scopes marking a scope as dynamic has no effect because the outer cache scope\n            // creates a cache boundary. This is subtly different from reading a dynamic data source which is\n            // forbidden inside a cache scope.\n            return;\n        }\n    }\n    // If we're forcing dynamic rendering or we're forcing static rendering, we\n    // don't need to do anything here because the entire page is already dynamic\n    // or it's static and it should not throw or postpone here.\n    if (store.forceDynamic || store.forceStatic) return;\n    if (store.dynamicShouldError) {\n        throw Object.defineProperty(new _client_components_static_generation_bailout__WEBPACK_IMPORTED_MODULE_2__.StaticGenBailoutError(`Route ${store.route} with \\`dynamic = \"error\"\\` couldn't be rendered statically because it used \\`${expression}\\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`), \"__NEXT_ERROR_CODE\", {\n            value: \"E553\",\n            enumerable: false,\n            configurable: true\n        });\n    }\n    if (workUnitStore) {\n        if (workUnitStore.type === 'prerender-ppr') {\n            postponeWithTracking(store.route, expression, workUnitStore.dynamicTracking);\n        } else if (workUnitStore.type === 'prerender-legacy') {\n            workUnitStore.revalidate = 0;\n            // We aren't prerendering but we are generating a static page. We need to bail out of static generation\n            const err = Object.defineProperty(new _client_components_hooks_server_context__WEBPACK_IMPORTED_MODULE_1__.DynamicServerError(`Route ${store.route} couldn't be rendered statically because it used ${expression}. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`), \"__NEXT_ERROR_CODE\", {\n                value: \"E550\",\n                enumerable: false,\n                configurable: true\n            });\n            store.dynamicUsageDescription = expression;\n            store.dynamicUsageStack = err.stack;\n            throw err;\n        } else if ( true && workUnitStore && workUnitStore.type === 'request') {\n            workUnitStore.usedDynamic = true;\n        }\n    }\n}\n/**\n * This function communicates that some dynamic path parameter was read. This\n * differs from the more general `trackDynamicDataAccessed` in that it is will\n * not error when `dynamic = \"error\"` is set.\n *\n * @param store The static generation store\n * @param expression The expression that was accessed dynamically\n */ function trackFallbackParamAccessed(store, expression) {\n    const prerenderStore = _work_unit_async_storage_external__WEBPACK_IMPORTED_MODULE_3__.workUnitAsyncStorage.getStore();\n    if (!prerenderStore || prerenderStore.type !== 'prerender-ppr') return;\n    postponeWithTracking(store.route, expression, prerenderStore.dynamicTracking);\n}\n/**\n * This function is meant to be used when prerendering without dynamicIO or PPR.\n * When called during a build it will cause Next.js to consider the route as dynamic.\n *\n * @internal\n */ function throwToInterruptStaticGeneration(expression, store, prerenderStore) {\n    // We aren't prerendering but we are generating a static page. We need to bail out of static generation\n    const err = Object.defineProperty(new _client_components_hooks_server_context__WEBPACK_IMPORTED_MODULE_1__.DynamicServerError(`Route ${store.route} couldn't be rendered statically because it used \\`${expression}\\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`), \"__NEXT_ERROR_CODE\", {\n        value: \"E558\",\n        enumerable: false,\n        configurable: true\n    });\n    prerenderStore.revalidate = 0;\n    store.dynamicUsageDescription = expression;\n    store.dynamicUsageStack = err.stack;\n    throw err;\n}\n/**\n * This function should be used to track whether something dynamic happened even when\n * we are in a dynamic render. This is useful for Dev where all renders are dynamic but\n * we still track whether dynamic APIs were accessed for helpful messaging\n *\n * @internal\n */ function trackDynamicDataInDynamicRender(_store, workUnitStore) {\n    if (workUnitStore) {\n        if (workUnitStore.type === 'cache' || workUnitStore.type === 'unstable-cache') {\n            // inside cache scopes marking a scope as dynamic has no effect because the outer cache scope\n            // creates a cache boundary. This is subtly different from reading a dynamic data source which is\n            // forbidden inside a cache scope.\n            return;\n        }\n        if (workUnitStore.type === 'prerender' || workUnitStore.type === 'prerender-legacy') {\n            workUnitStore.revalidate = 0;\n        }\n        if ( true && workUnitStore.type === 'request') {\n            workUnitStore.usedDynamic = true;\n        }\n    }\n}\n// Despite it's name we don't actually abort unless we have a controller to call abort on\n// There are times when we let a prerender run long to discover caches where we want the semantics\n// of tracking dynamic access without terminating the prerender early\nfunction abortOnSynchronousDynamicDataAccess(route, expression, prerenderStore) {\n    const reason = `Route ${route} needs to bail out of prerendering at this point because it used ${expression}.`;\n    const error = createPrerenderInterruptedError(reason);\n    prerenderStore.controller.abort(error);\n    const dynamicTracking = prerenderStore.dynamicTracking;\n    if (dynamicTracking) {\n        dynamicTracking.dynamicAccesses.push({\n            // When we aren't debugging, we don't need to create another error for the\n            // stack trace.\n            stack: dynamicTracking.isDebugDynamicAccesses ? new Error().stack : undefined,\n            expression\n        });\n    }\n}\nfunction abortOnSynchronousPlatformIOAccess(route, expression, errorWithStack, prerenderStore) {\n    const dynamicTracking = prerenderStore.dynamicTracking;\n    if (dynamicTracking) {\n        if (dynamicTracking.syncDynamicErrorWithStack === null) {\n            dynamicTracking.syncDynamicExpression = expression;\n            dynamicTracking.syncDynamicErrorWithStack = errorWithStack;\n        }\n    }\n    abortOnSynchronousDynamicDataAccess(route, expression, prerenderStore);\n}\nfunction trackSynchronousPlatformIOAccessInDev(requestStore) {\n    // We don't actually have a controller to abort but we do the semantic equivalent by\n    // advancing the request store out of prerender mode\n    requestStore.prerenderPhase = false;\n}\n/**\n * use this function when prerendering with dynamicIO. If we are doing a\n * prospective prerender we don't actually abort because we want to discover\n * all caches for the shell. If this is the actual prerender we do abort.\n *\n * This function accepts a prerenderStore but the caller should ensure we're\n * actually running in dynamicIO mode.\n *\n * @internal\n */ function abortAndThrowOnSynchronousRequestDataAccess(route, expression, errorWithStack, prerenderStore) {\n    const prerenderSignal = prerenderStore.controller.signal;\n    if (prerenderSignal.aborted === false) {\n        // TODO it would be better to move this aborted check into the callsite so we can avoid making\n        // the error object when it isn't relevant to the aborting of the prerender however\n        // since we need the throw semantics regardless of whether we abort it is easier to land\n        // this way. See how this was handled with `abortOnSynchronousPlatformIOAccess` for a closer\n        // to ideal implementation\n        const dynamicTracking = prerenderStore.dynamicTracking;\n        if (dynamicTracking) {\n            if (dynamicTracking.syncDynamicErrorWithStack === null) {\n                dynamicTracking.syncDynamicExpression = expression;\n                dynamicTracking.syncDynamicErrorWithStack = errorWithStack;\n                if (prerenderStore.validating === true) {\n                    // We always log Request Access in dev at the point of calling the function\n                    // So we mark the dynamic validation as not requiring it to be printed\n                    dynamicTracking.syncDynamicLogged = true;\n                }\n            }\n        }\n        abortOnSynchronousDynamicDataAccess(route, expression, prerenderStore);\n    }\n    throw createPrerenderInterruptedError(`Route ${route} needs to bail out of prerendering at this point because it used ${expression}.`);\n}\n// For now these implementations are the same so we just reexport\nconst trackSynchronousRequestDataAccessInDev = trackSynchronousPlatformIOAccessInDev;\nfunction Postpone({ reason, route }) {\n    const prerenderStore = _work_unit_async_storage_external__WEBPACK_IMPORTED_MODULE_3__.workUnitAsyncStorage.getStore();\n    const dynamicTracking = prerenderStore && prerenderStore.type === 'prerender-ppr' ? prerenderStore.dynamicTracking : null;\n    postponeWithTracking(route, reason, dynamicTracking);\n}\nfunction postponeWithTracking(route, expression, dynamicTracking) {\n    assertPostpone();\n    if (dynamicTracking) {\n        dynamicTracking.dynamicAccesses.push({\n            // When we aren't debugging, we don't need to create another error for the\n            // stack trace.\n            stack: dynamicTracking.isDebugDynamicAccesses ? new Error().stack : undefined,\n            expression\n        });\n    }\n    react__WEBPACK_IMPORTED_MODULE_0__.unstable_postpone(createPostponeReason(route, expression));\n}\nfunction createPostponeReason(route, expression) {\n    return `Route ${route} needs to bail out of prerendering at this point because it used ${expression}. ` + `React throws this special object to indicate where. It should not be caught by ` + `your own try/catch. Learn more: https://nextjs.org/docs/messages/ppr-caught-error`;\n}\nfunction isDynamicPostpone(err) {\n    if (typeof err === 'object' && err !== null && typeof err.message === 'string') {\n        return isDynamicPostponeReason(err.message);\n    }\n    return false;\n}\nfunction isDynamicPostponeReason(reason) {\n    return reason.includes('needs to bail out of prerendering at this point because it used') && reason.includes('Learn more: https://nextjs.org/docs/messages/ppr-caught-error');\n}\nif (isDynamicPostponeReason(createPostponeReason('%%%', '^^^')) === false) {\n    throw Object.defineProperty(new Error('Invariant: isDynamicPostpone misidentified a postpone reason. This is a bug in Next.js'), \"__NEXT_ERROR_CODE\", {\n        value: \"E296\",\n        enumerable: false,\n        configurable: true\n    });\n}\nconst NEXT_PRERENDER_INTERRUPTED = 'NEXT_PRERENDER_INTERRUPTED';\nfunction createPrerenderInterruptedError(message) {\n    const error = Object.defineProperty(new Error(message), \"__NEXT_ERROR_CODE\", {\n        value: \"E394\",\n        enumerable: false,\n        configurable: true\n    });\n    error.digest = NEXT_PRERENDER_INTERRUPTED;\n    return error;\n}\nfunction isPrerenderInterruptedError(error) {\n    return typeof error === 'object' && error !== null && error.digest === NEXT_PRERENDER_INTERRUPTED && 'name' in error && 'message' in error && error instanceof Error;\n}\nfunction accessedDynamicData(dynamicAccesses) {\n    return dynamicAccesses.length > 0;\n}\nfunction consumeDynamicAccess(serverDynamic, clientDynamic) {\n    // We mutate because we only call this once we are no longer writing\n    // to the dynamicTrackingState and it's more efficient than creating a new\n    // array.\n    serverDynamic.dynamicAccesses.push(...clientDynamic.dynamicAccesses);\n    return serverDynamic.dynamicAccesses;\n}\nfunction formatDynamicAPIAccesses(dynamicAccesses) {\n    return dynamicAccesses.filter((access)=>typeof access.stack === 'string' && access.stack.length > 0).map(({ expression, stack })=>{\n        stack = stack.split('\\n')// Remove the \"Error: \" prefix from the first line of the stack trace as\n        // well as the first 4 lines of the stack trace which is the distance\n        // from the user code and the `new Error().stack` call.\n        .slice(4).filter((line)=>{\n            // Exclude Next.js internals from the stack trace.\n            if (line.includes('node_modules/next/')) {\n                return false;\n            }\n            // Exclude anonymous functions from the stack trace.\n            if (line.includes(' (<anonymous>)')) {\n                return false;\n            }\n            // Exclude Node.js internals from the stack trace.\n            if (line.includes(' (node:')) {\n                return false;\n            }\n            return true;\n        }).join('\\n');\n        return `Dynamic API Usage Debug - ${expression}:\\n${stack}`;\n    });\n}\nfunction assertPostpone() {\n    if (!hasPostpone) {\n        throw Object.defineProperty(new Error(`Invariant: React.unstable_postpone is not defined. This suggests the wrong version of React was loaded. This is a bug in Next.js`), \"__NEXT_ERROR_CODE\", {\n            value: \"E224\",\n            enumerable: false,\n            configurable: true\n        });\n    }\n}\n/**\n * This is a bit of a hack to allow us to abort a render using a Postpone instance instead of an Error which changes React's\n * abort semantics slightly.\n */ function createPostponedAbortSignal(reason) {\n    assertPostpone();\n    const controller = new AbortController();\n    // We get our hands on a postpone instance by calling postpone and catching the throw\n    try {\n        react__WEBPACK_IMPORTED_MODULE_0__.unstable_postpone(reason);\n    } catch (x) {\n        controller.abort(x);\n    }\n    return controller.signal;\n}\n/**\n * In a prerender, we may end up with hanging Promises as inputs due them\n * stalling on connection() or because they're loading dynamic data. In that\n * case we need to abort the encoding of arguments since they'll never complete.\n */ function createHangingInputAbortSignal(workUnitStore) {\n    const controller = new AbortController();\n    if (workUnitStore.cacheSignal) {\n        // If we have a cacheSignal it means we're in a prospective render. If the input\n        // we're waiting on is coming from another cache, we do want to wait for it so that\n        // we can resolve this cache entry too.\n        workUnitStore.cacheSignal.inputReady().then(()=>{\n            controller.abort();\n        });\n    } else {\n        // Otherwise we're in the final render and we should already have all our caches\n        // filled. We might still be waiting on some microtasks so we wait one tick before\n        // giving up. When we give up, we still want to render the content of this cache\n        // as deeply as we can so that we can suspend as deeply as possible in the tree\n        // or not at all if we don't end up waiting for the input.\n        (0,_lib_scheduler__WEBPACK_IMPORTED_MODULE_7__.scheduleOnNextTick)(()=>controller.abort());\n    }\n    return controller.signal;\n}\nfunction annotateDynamicAccess(expression, prerenderStore) {\n    const dynamicTracking = prerenderStore.dynamicTracking;\n    if (dynamicTracking) {\n        dynamicTracking.dynamicAccesses.push({\n            stack: dynamicTracking.isDebugDynamicAccesses ? new Error().stack : undefined,\n            expression\n        });\n    }\n}\nfunction useDynamicRouteParams(expression) {\n    const workStore = _app_render_work_async_storage_external__WEBPACK_IMPORTED_MODULE_4__.workAsyncStorage.getStore();\n    if (workStore && workStore.isStaticGeneration && workStore.fallbackRouteParams && workStore.fallbackRouteParams.size > 0) {\n        // There are fallback route params, we should track these as dynamic\n        // accesses.\n        const workUnitStore = _work_unit_async_storage_external__WEBPACK_IMPORTED_MODULE_3__.workUnitAsyncStorage.getStore();\n        if (workUnitStore) {\n            // We're prerendering with dynamicIO or PPR or both\n            if (workUnitStore.type === 'prerender') {\n                // We are in a prerender with dynamicIO semantics\n                // We are going to hang here and never resolve. This will cause the currently\n                // rendering component to effectively be a dynamic hole\n                react__WEBPACK_IMPORTED_MODULE_0__.use((0,_dynamic_rendering_utils__WEBPACK_IMPORTED_MODULE_5__.makeHangingPromise)(workUnitStore.renderSignal, expression));\n            } else if (workUnitStore.type === 'prerender-ppr') {\n                // We're prerendering with PPR\n                postponeWithTracking(workStore.route, expression, workUnitStore.dynamicTracking);\n            } else if (workUnitStore.type === 'prerender-legacy') {\n                throwToInterruptStaticGeneration(expression, workStore, workUnitStore);\n            }\n        }\n    }\n}\nconst hasSuspenseRegex = /\\n\\s+at Suspense \\(<anonymous>\\)/;\nconst hasMetadataRegex = new RegExp(`\\\\n\\\\s+at ${_lib_metadata_metadata_constants__WEBPACK_IMPORTED_MODULE_6__.METADATA_BOUNDARY_NAME}[\\\\n\\\\s]`);\nconst hasViewportRegex = new RegExp(`\\\\n\\\\s+at ${_lib_metadata_metadata_constants__WEBPACK_IMPORTED_MODULE_6__.VIEWPORT_BOUNDARY_NAME}[\\\\n\\\\s]`);\nconst hasOutletRegex = new RegExp(`\\\\n\\\\s+at ${_lib_metadata_metadata_constants__WEBPACK_IMPORTED_MODULE_6__.OUTLET_BOUNDARY_NAME}[\\\\n\\\\s]`);\nfunction trackAllowedDynamicAccess(route, componentStack, dynamicValidation, serverDynamic, clientDynamic) {\n    if (hasOutletRegex.test(componentStack)) {\n        // We don't need to track that this is dynamic. It is only so when something else is also dynamic.\n        return;\n    } else if (hasMetadataRegex.test(componentStack)) {\n        dynamicValidation.hasDynamicMetadata = true;\n        return;\n    } else if (hasViewportRegex.test(componentStack)) {\n        dynamicValidation.hasDynamicViewport = true;\n        return;\n    } else if (hasSuspenseRegex.test(componentStack)) {\n        dynamicValidation.hasSuspendedDynamic = true;\n        return;\n    } else if (serverDynamic.syncDynamicErrorWithStack || clientDynamic.syncDynamicErrorWithStack) {\n        dynamicValidation.hasSyncDynamicErrors = true;\n        return;\n    } else {\n        const message = `Route \"${route}\": A component accessed data, headers, params, searchParams, or a short-lived cache without a Suspense boundary nor a \"use cache\" above it. We don't have the exact line number added to error messages yet but you can see which component in the stack below. See more info: https://nextjs.org/docs/messages/next-prerender-missing-suspense`;\n        const error = createErrorWithComponentStack(message, componentStack);\n        dynamicValidation.dynamicErrors.push(error);\n        return;\n    }\n}\nfunction createErrorWithComponentStack(message, componentStack) {\n    const error = Object.defineProperty(new Error(message), \"__NEXT_ERROR_CODE\", {\n        value: \"E394\",\n        enumerable: false,\n        configurable: true\n    });\n    error.stack = 'Error: ' + message + componentStack;\n    return error;\n}\nfunction throwIfDisallowedDynamic(route, dynamicValidation, serverDynamic, clientDynamic) {\n    let syncError;\n    let syncExpression;\n    let syncLogged;\n    if (serverDynamic.syncDynamicErrorWithStack) {\n        syncError = serverDynamic.syncDynamicErrorWithStack;\n        syncExpression = serverDynamic.syncDynamicExpression;\n        syncLogged = serverDynamic.syncDynamicLogged === true;\n    } else if (clientDynamic.syncDynamicErrorWithStack) {\n        syncError = clientDynamic.syncDynamicErrorWithStack;\n        syncExpression = clientDynamic.syncDynamicExpression;\n        syncLogged = clientDynamic.syncDynamicLogged === true;\n    } else {\n        syncError = null;\n        syncExpression = undefined;\n        syncLogged = false;\n    }\n    if (dynamicValidation.hasSyncDynamicErrors && syncError) {\n        if (!syncLogged) {\n            // In dev we already log errors about sync dynamic access. But during builds we need to ensure\n            // the offending sync error is logged before we exit the build\n            console.error(syncError);\n        }\n        // The actual error should have been logged when the sync access ocurred\n        throw new _client_components_static_generation_bailout__WEBPACK_IMPORTED_MODULE_2__.StaticGenBailoutError();\n    }\n    const dynamicErrors = dynamicValidation.dynamicErrors;\n    if (dynamicErrors.length) {\n        for(let i = 0; i < dynamicErrors.length; i++){\n            console.error(dynamicErrors[i]);\n        }\n        throw new _client_components_static_generation_bailout__WEBPACK_IMPORTED_MODULE_2__.StaticGenBailoutError();\n    }\n    if (!dynamicValidation.hasSuspendedDynamic) {\n        if (dynamicValidation.hasDynamicMetadata) {\n            if (syncError) {\n                console.error(syncError);\n                throw Object.defineProperty(new _client_components_static_generation_bailout__WEBPACK_IMPORTED_MODULE_2__.StaticGenBailoutError(`Route \"${route}\" has a \\`generateMetadata\\` that could not finish rendering before ${syncExpression} was used. Follow the instructions in the error for this expression to resolve.`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E608\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            throw Object.defineProperty(new _client_components_static_generation_bailout__WEBPACK_IMPORTED_MODULE_2__.StaticGenBailoutError(`Route \"${route}\" has a \\`generateMetadata\\` that depends on Request data (\\`cookies()\\`, etc...) or external data (\\`fetch(...)\\`, etc...) but the rest of the route was static or only used cached data (\\`\"use cache\"\\`). If you expected this route to be prerenderable update your \\`generateMetadata\\` to not use Request data and only use cached external data. Otherwise, add \\`await connection()\\` somewhere within this route to indicate explicitly it should not be prerendered.`), \"__NEXT_ERROR_CODE\", {\n                value: \"E534\",\n                enumerable: false,\n                configurable: true\n            });\n        } else if (dynamicValidation.hasDynamicViewport) {\n            if (syncError) {\n                console.error(syncError);\n                throw Object.defineProperty(new _client_components_static_generation_bailout__WEBPACK_IMPORTED_MODULE_2__.StaticGenBailoutError(`Route \"${route}\" has a \\`generateViewport\\` that could not finish rendering before ${syncExpression} was used. Follow the instructions in the error for this expression to resolve.`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E573\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            throw Object.defineProperty(new _client_components_static_generation_bailout__WEBPACK_IMPORTED_MODULE_2__.StaticGenBailoutError(`Route \"${route}\" has a \\`generateViewport\\` that depends on Request data (\\`cookies()\\`, etc...) or external data (\\`fetch(...)\\`, etc...) but the rest of the route was static or only used cached data (\\`\"use cache\"\\`). If you expected this route to be prerenderable update your \\`generateViewport\\` to not use Request data and only use cached external data. Otherwise, add \\`await connection()\\` somewhere within this route to indicate explicitly it should not be prerendered.`), \"__NEXT_ERROR_CODE\", {\n                value: \"E590\",\n                enumerable: false,\n                configurable: true\n            });\n        }\n    }\n}\n\n//# sourceMappingURL=dynamic-rendering.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./node_modules/next/dist/esm/server/app-render/dynamic-rendering.js\n");

/***/ }),

/***/ "(middleware)/./node_modules/next/dist/esm/server/request/connection.js":
/*!*****************************************************************!*\
  !*** ./node_modules/next/dist/esm/server/request/connection.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   connection: () => (/* binding */ connection)\n/* harmony export */ });\n/* harmony import */ var _app_render_work_async_storage_external__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../app-render/work-async-storage.external */ \"(shared)/./node_modules/next/dist/esm/server/app-render/work-async-storage.external.js?73ab\");\n/* harmony import */ var _app_render_work_unit_async_storage_external__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../app-render/work-unit-async-storage.external */ \"(shared)/./node_modules/next/dist/esm/server/app-render/work-unit-async-storage.external.js?418e\");\n/* harmony import */ var _app_render_dynamic_rendering__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../app-render/dynamic-rendering */ \"(middleware)/./node_modules/next/dist/esm/server/app-render/dynamic-rendering.js\");\n/* harmony import */ var _client_components_static_generation_bailout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../client/components/static-generation-bailout */ \"(middleware)/./node_modules/next/dist/esm/client/components/static-generation-bailout.js\");\n/* harmony import */ var _dynamic_rendering_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../dynamic-rendering-utils */ \"(middleware)/./node_modules/next/dist/esm/server/dynamic-rendering-utils.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./utils */ \"(middleware)/./node_modules/next/dist/esm/server/request/utils.js\");\n\n\n\n\n\n\n/**\n * This function allows you to indicate that you require an actual user Request before continuing.\n *\n * During prerendering it will never resolve and during rendering it resolves immediately.\n */ function connection() {\n    const workStore = _app_render_work_async_storage_external__WEBPACK_IMPORTED_MODULE_0__.workAsyncStorage.getStore();\n    const workUnitStore = _app_render_work_unit_async_storage_external__WEBPACK_IMPORTED_MODULE_1__.workUnitAsyncStorage.getStore();\n    if (workStore) {\n        if (workUnitStore && workUnitStore.phase === 'after' && !(0,_utils__WEBPACK_IMPORTED_MODULE_5__.isRequestAPICallableInsideAfter)()) {\n            throw Object.defineProperty(new Error(`Route ${workStore.route} used \"connection\" inside \"after(...)\". The \\`connection()\\` function is used to indicate the subsequent code must only run when there is an actual Request, but \"after(...)\" executes after the request, so this function is not allowed in this scope. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after`), \"__NEXT_ERROR_CODE\", {\n                value: \"E186\",\n                enumerable: false,\n                configurable: true\n            });\n        }\n        if (workStore.forceStatic) {\n            // When using forceStatic we override all other logic and always just return an empty\n            // headers object without tracking\n            return Promise.resolve(undefined);\n        }\n        if (workUnitStore) {\n            if (workUnitStore.type === 'cache') {\n                throw Object.defineProperty(new Error(`Route ${workStore.route} used \"connection\" inside \"use cache\". The \\`connection()\\` function is used to indicate the subsequent code must only run when there is an actual Request, but caches must be able to be produced before a Request so this function is not allowed in this scope. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E111\",\n                    enumerable: false,\n                    configurable: true\n                });\n            } else if (workUnitStore.type === 'unstable-cache') {\n                throw Object.defineProperty(new Error(`Route ${workStore.route} used \"connection\" inside a function cached with \"unstable_cache(...)\". The \\`connection()\\` function is used to indicate the subsequent code must only run when there is an actual Request, but caches must be able to be produced before a Request so this function is not allowed in this scope. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E1\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n        }\n        if (workStore.dynamicShouldError) {\n            throw Object.defineProperty(new _client_components_static_generation_bailout__WEBPACK_IMPORTED_MODULE_3__.StaticGenBailoutError(`Route ${workStore.route} with \\`dynamic = \"error\"\\` couldn't be rendered statically because it used \\`connection\\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`), \"__NEXT_ERROR_CODE\", {\n                value: \"E562\",\n                enumerable: false,\n                configurable: true\n            });\n        }\n        if (workUnitStore) {\n            if (workUnitStore.type === 'prerender') {\n                // dynamicIO Prerender\n                // We return a promise that never resolves to allow the prender to stall at this point\n                return (0,_dynamic_rendering_utils__WEBPACK_IMPORTED_MODULE_4__.makeHangingPromise)(workUnitStore.renderSignal, '`connection()`');\n            } else if (workUnitStore.type === 'prerender-ppr') {\n                // PPR Prerender (no dynamicIO)\n                // We use React's postpone API to interrupt rendering here to create a dynamic hole\n                (0,_app_render_dynamic_rendering__WEBPACK_IMPORTED_MODULE_2__.postponeWithTracking)(workStore.route, 'connection', workUnitStore.dynamicTracking);\n            } else if (workUnitStore.type === 'prerender-legacy') {\n                // Legacy Prerender\n                // We throw an error here to interrupt prerendering to mark the route as dynamic\n                (0,_app_render_dynamic_rendering__WEBPACK_IMPORTED_MODULE_2__.throwToInterruptStaticGeneration)('connection', workStore, workUnitStore);\n            }\n        }\n        // We fall through to the dynamic context below but we still track dynamic access\n        // because in dev we can still error for things like using headers inside a cache context\n        (0,_app_render_dynamic_rendering__WEBPACK_IMPORTED_MODULE_2__.trackDynamicDataInDynamicRender)(workStore, workUnitStore);\n    }\n    return Promise.resolve(undefined);\n}\n\n//# sourceMappingURL=connection.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./node_modules/next/dist/esm/server/request/connection.js\n");

/***/ }),

/***/ "(middleware)/./node_modules/next/dist/esm/server/request/root-params.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/esm/server/request/root-params.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   unstable_rootParams: () => (/* binding */ unstable_rootParams)\n/* harmony export */ });\n/* harmony import */ var _shared_lib_invariant_error__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../shared/lib/invariant-error */ \"(middleware)/./node_modules/next/dist/esm/shared/lib/invariant-error.js\");\n/* harmony import */ var _app_render_dynamic_rendering__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../app-render/dynamic-rendering */ \"(middleware)/./node_modules/next/dist/esm/server/app-render/dynamic-rendering.js\");\n/* harmony import */ var _app_render_work_async_storage_external__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../app-render/work-async-storage.external */ \"(shared)/./node_modules/next/dist/esm/server/app-render/work-async-storage.external.js?73ab\");\n/* harmony import */ var _app_render_work_unit_async_storage_external__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../app-render/work-unit-async-storage.external */ \"(shared)/./node_modules/next/dist/esm/server/app-render/work-unit-async-storage.external.js?418e\");\n/* harmony import */ var _dynamic_rendering_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../dynamic-rendering-utils */ \"(middleware)/./node_modules/next/dist/esm/server/dynamic-rendering-utils.js\");\n/* harmony import */ var _shared_lib_utils_reflect_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../shared/lib/utils/reflect-utils */ \"(middleware)/./node_modules/next/dist/esm/shared/lib/utils/reflect-utils.js\");\n\n\n\n\n\n\nconst CachedParams = new WeakMap();\nasync function unstable_rootParams() {\n    const workStore = _app_render_work_async_storage_external__WEBPACK_IMPORTED_MODULE_2__.workAsyncStorage.getStore();\n    if (!workStore) {\n        throw Object.defineProperty(new _shared_lib_invariant_error__WEBPACK_IMPORTED_MODULE_0__.InvariantError('Missing workStore in unstable_rootParams'), \"__NEXT_ERROR_CODE\", {\n            value: \"E615\",\n            enumerable: false,\n            configurable: true\n        });\n    }\n    const workUnitStore = _app_render_work_unit_async_storage_external__WEBPACK_IMPORTED_MODULE_3__.workUnitAsyncStorage.getStore();\n    if (!workUnitStore) {\n        throw Object.defineProperty(new Error(`Route ${workStore.route} used \\`unstable_rootParams()\\` in Pages Router. This API is only available within App Router.`), \"__NEXT_ERROR_CODE\", {\n            value: \"E641\",\n            enumerable: false,\n            configurable: true\n        });\n    }\n    switch(workUnitStore.type){\n        case 'unstable-cache':\n        case 'cache':\n            {\n                throw Object.defineProperty(new Error(`Route ${workStore.route} used \\`unstable_rootParams()\\` inside \\`\"use cache\"\\` or \\`unstable_cache\\`. Support for this API inside cache scopes is planned for a future version of Next.js.`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E642\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n        case 'prerender':\n        case 'prerender-ppr':\n        case 'prerender-legacy':\n            return createPrerenderRootParams(workUnitStore.rootParams, workStore, workUnitStore);\n        default:\n            return Promise.resolve(workUnitStore.rootParams);\n    }\n}\nfunction createPrerenderRootParams(underlyingParams, workStore, prerenderStore) {\n    const fallbackParams = workStore.fallbackRouteParams;\n    if (fallbackParams) {\n        let hasSomeFallbackParams = false;\n        for(const key in underlyingParams){\n            if (fallbackParams.has(key)) {\n                hasSomeFallbackParams = true;\n                break;\n            }\n        }\n        if (hasSomeFallbackParams) {\n            // params need to be treated as dynamic because we have at least one fallback param\n            if (prerenderStore.type === 'prerender') {\n                // We are in a dynamicIO (PPR or otherwise) prerender\n                const cachedParams = CachedParams.get(underlyingParams);\n                if (cachedParams) {\n                    return cachedParams;\n                }\n                const promise = (0,_dynamic_rendering_utils__WEBPACK_IMPORTED_MODULE_4__.makeHangingPromise)(prerenderStore.renderSignal, '`unstable_rootParams`');\n                CachedParams.set(underlyingParams, promise);\n                return promise;\n            }\n            // remaining cases are prerender-ppr and prerender-legacy\n            // We aren't in a dynamicIO prerender but we do have fallback params at this\n            // level so we need to make an erroring params object which will postpone\n            // if you access the fallback params\n            return makeErroringRootParams(underlyingParams, fallbackParams, workStore, prerenderStore);\n        }\n    }\n    // We don't have any fallback params so we have an entirely static safe params object\n    return Promise.resolve(underlyingParams);\n}\nfunction makeErroringRootParams(underlyingParams, fallbackParams, workStore, prerenderStore) {\n    const cachedParams = CachedParams.get(underlyingParams);\n    if (cachedParams) {\n        return cachedParams;\n    }\n    const augmentedUnderlying = {\n        ...underlyingParams\n    };\n    // We don't use makeResolvedReactPromise here because params\n    // supports copying with spread and we don't want to unnecessarily\n    // instrument the promise with spreadable properties of ReactPromise.\n    const promise = Promise.resolve(augmentedUnderlying);\n    CachedParams.set(underlyingParams, promise);\n    Object.keys(underlyingParams).forEach((prop)=>{\n        if (_shared_lib_utils_reflect_utils__WEBPACK_IMPORTED_MODULE_5__.wellKnownProperties.has(prop)) {\n        // These properties cannot be shadowed because they need to be the\n        // true underlying value for Promises to work correctly at runtime\n        } else {\n            if (fallbackParams.has(prop)) {\n                Object.defineProperty(augmentedUnderlying, prop, {\n                    get () {\n                        const expression = (0,_shared_lib_utils_reflect_utils__WEBPACK_IMPORTED_MODULE_5__.describeStringPropertyAccess)('unstable_rootParams', prop);\n                        // In most dynamic APIs we also throw if `dynamic = \"error\"` however\n                        // for params is only dynamic when we're generating a fallback shell\n                        // and even when `dynamic = \"error\"` we still support generating dynamic\n                        // fallback shells\n                        // TODO remove this comment when dynamicIO is the default since there\n                        // will be no `dynamic = \"error\"`\n                        if (prerenderStore.type === 'prerender-ppr') {\n                            // PPR Prerender (no dynamicIO)\n                            (0,_app_render_dynamic_rendering__WEBPACK_IMPORTED_MODULE_1__.postponeWithTracking)(workStore.route, expression, prerenderStore.dynamicTracking);\n                        } else {\n                            // Legacy Prerender\n                            (0,_app_render_dynamic_rendering__WEBPACK_IMPORTED_MODULE_1__.throwToInterruptStaticGeneration)(expression, workStore, prerenderStore);\n                        }\n                    },\n                    enumerable: true\n                });\n            } else {\n                ;\n                promise[prop] = underlyingParams[prop];\n            }\n        }\n    });\n    return promise;\n}\n\n//# sourceMappingURL=root-params.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./node_modules/next/dist/esm/server/request/root-params.js\n");

/***/ }),

/***/ "(middleware)/./node_modules/next/dist/esm/server/web/adapter.js":
/*!**********************************************************!*\
  !*** ./node_modules/next/dist/esm/server/web/adapter.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NextRequestHint: () => (/* binding */ NextRequestHint),\n/* harmony export */   adapter: () => (/* binding */ adapter)\n/* harmony export */ });\n/* harmony import */ var _error__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./error */ \"(middleware)/./node_modules/next/dist/esm/server/web/error.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils */ \"(middleware)/./node_modules/next/dist/esm/server/web/utils.js\");\n/* harmony import */ var _spec_extension_fetch_event__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./spec-extension/fetch-event */ \"(middleware)/./node_modules/next/dist/esm/server/web/spec-extension/fetch-event.js\");\n/* harmony import */ var _spec_extension_request__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./spec-extension/request */ \"(middleware)/./node_modules/next/dist/esm/server/web/spec-extension/request.js\");\n/* harmony import */ var _spec_extension_response__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./spec-extension/response */ \"(middleware)/./node_modules/next/dist/esm/server/web/spec-extension/response.js\");\n/* harmony import */ var _shared_lib_router_utils_relativize_url__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../shared/lib/router/utils/relativize-url */ \"(middleware)/./node_modules/next/dist/esm/shared/lib/router/utils/relativize-url.js\");\n/* harmony import */ var _next_url__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./next-url */ \"(middleware)/./node_modules/next/dist/esm/server/web/next-url.js\");\n/* harmony import */ var _internal_utils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../internal-utils */ \"(middleware)/./node_modules/next/dist/esm/server/internal-utils.js\");\n/* harmony import */ var _shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../shared/lib/router/utils/app-paths */ \"(middleware)/./node_modules/next/dist/esm/shared/lib/router/utils/app-paths.js\");\n/* harmony import */ var _client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../client/components/app-router-headers */ \"(middleware)/./node_modules/next/dist/esm/client/components/app-router-headers.js\");\n/* harmony import */ var _globals__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./globals */ \"(middleware)/./node_modules/next/dist/esm/server/web/globals.js\");\n/* harmony import */ var _async_storage_request_store__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../async-storage/request-store */ \"(middleware)/./node_modules/next/dist/esm/server/async-storage/request-store.js\");\n/* harmony import */ var _app_render_work_unit_async_storage_external__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../app-render/work-unit-async-storage.external */ \"(shared)/./node_modules/next/dist/esm/server/app-render/work-unit-async-storage.external.js?418e\");\n/* harmony import */ var _async_storage_work_store__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../async-storage/work-store */ \"(middleware)/./node_modules/next/dist/esm/server/async-storage/work-store.js\");\n/* harmony import */ var _app_render_work_async_storage_external__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ../app-render/work-async-storage.external */ \"(shared)/./node_modules/next/dist/esm/server/app-render/work-async-storage.external.js?73ab\");\n/* harmony import */ var _lib_trace_tracer__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ../lib/trace/tracer */ \"(middleware)/./node_modules/next/dist/esm/server/lib/trace/tracer.js\");\n/* harmony import */ var _lib_trace_constants__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ../lib/trace/constants */ \"(middleware)/./node_modules/next/dist/esm/server/lib/trace/constants.js\");\n/* harmony import */ var _web_on_close__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./web-on-close */ \"(middleware)/./node_modules/next/dist/esm/server/web/web-on-close.js\");\n/* harmony import */ var _get_edge_preview_props__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ./get-edge-preview-props */ \"(middleware)/./node_modules/next/dist/esm/server/web/get-edge-preview-props.js\");\n/* harmony import */ var _after_builtin_request_context__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ../after/builtin-request-context */ \"(middleware)/./node_modules/next/dist/esm/server/after/builtin-request-context.js\");\n/* harmony import */ var _lib_implicit_tags__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! ../lib/implicit-tags */ \"(middleware)/./node_modules/next/dist/esm/server/lib/implicit-tags.js\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nclass NextRequestHint extends _spec_extension_request__WEBPACK_IMPORTED_MODULE_3__.NextRequest {\n    constructor(params){\n        super(params.input, params.init);\n        this.sourcePage = params.page;\n    }\n    get request() {\n        throw Object.defineProperty(new _error__WEBPACK_IMPORTED_MODULE_0__.PageSignatureError({\n            page: this.sourcePage\n        }), \"__NEXT_ERROR_CODE\", {\n            value: \"E394\",\n            enumerable: false,\n            configurable: true\n        });\n    }\n    respondWith() {\n        throw Object.defineProperty(new _error__WEBPACK_IMPORTED_MODULE_0__.PageSignatureError({\n            page: this.sourcePage\n        }), \"__NEXT_ERROR_CODE\", {\n            value: \"E394\",\n            enumerable: false,\n            configurable: true\n        });\n    }\n    waitUntil() {\n        throw Object.defineProperty(new _error__WEBPACK_IMPORTED_MODULE_0__.PageSignatureError({\n            page: this.sourcePage\n        }), \"__NEXT_ERROR_CODE\", {\n            value: \"E394\",\n            enumerable: false,\n            configurable: true\n        });\n    }\n}\nconst headersGetter = {\n    keys: (headers)=>Array.from(headers.keys()),\n    get: (headers, key)=>headers.get(key) ?? undefined\n};\nlet propagator = (request, fn)=>{\n    const tracer = (0,_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_15__.getTracer)();\n    return tracer.withPropagatedContext(request.headers, fn, headersGetter);\n};\nlet testApisIntercepted = false;\nfunction ensureTestApisIntercepted() {\n    if (!testApisIntercepted) {\n        testApisIntercepted = true;\n        if (process.env.NEXT_PRIVATE_TEST_PROXY === 'true') {\n            const { interceptTestApis, wrapRequestHandler } = __webpack_require__(/*! next/dist/experimental/testmode/server-edge */ \"(middleware)/./node_modules/next/dist/experimental/testmode/server-edge.js\");\n            interceptTestApis();\n            propagator = wrapRequestHandler(propagator);\n        }\n    }\n}\nasync function adapter(params) {\n    var _getBuiltinRequestContext;\n    ensureTestApisIntercepted();\n    await (0,_globals__WEBPACK_IMPORTED_MODULE_10__.ensureInstrumentationRegistered)();\n    // TODO-APP: use explicit marker for this\n    const isEdgeRendering = typeof globalThis.__BUILD_MANIFEST !== 'undefined';\n    params.request.url = (0,_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_8__.normalizeRscURL)(params.request.url);\n    const requestURL = new _next_url__WEBPACK_IMPORTED_MODULE_6__.NextURL(params.request.url, {\n        headers: params.request.headers,\n        nextConfig: params.request.nextConfig\n    });\n    // Iterator uses an index to keep track of the current iteration. Because of deleting and appending below we can't just use the iterator.\n    // Instead we use the keys before iteration.\n    const keys = [\n        ...requestURL.searchParams.keys()\n    ];\n    for (const key of keys){\n        const value = requestURL.searchParams.getAll(key);\n        const normalizedKey = (0,_utils__WEBPACK_IMPORTED_MODULE_1__.normalizeNextQueryParam)(key);\n        if (normalizedKey) {\n            requestURL.searchParams.delete(normalizedKey);\n            for (const val of value){\n                requestURL.searchParams.append(normalizedKey, val);\n            }\n            requestURL.searchParams.delete(key);\n        }\n    }\n    // Ensure users only see page requests, never data requests.\n    const buildId = requestURL.buildId;\n    requestURL.buildId = '';\n    const requestHeaders = (0,_utils__WEBPACK_IMPORTED_MODULE_1__.fromNodeOutgoingHttpHeaders)(params.request.headers);\n    const isNextDataRequest = requestHeaders.has('x-nextjs-data');\n    const isRSCRequest = requestHeaders.get(_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_9__.RSC_HEADER) === '1';\n    if (isNextDataRequest && requestURL.pathname === '/index') {\n        requestURL.pathname = '/';\n    }\n    const flightHeaders = new Map();\n    // Headers should only be stripped for middleware\n    if (!isEdgeRendering) {\n        for (const header of _client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_9__.FLIGHT_HEADERS){\n            const key = header.toLowerCase();\n            const value = requestHeaders.get(key);\n            if (value !== null) {\n                flightHeaders.set(key, value);\n                requestHeaders.delete(key);\n            }\n        }\n    }\n    const normalizeURL =  false ? 0 : requestURL;\n    const request = new NextRequestHint({\n        page: params.page,\n        // Strip internal query parameters off the request.\n        input: (0,_internal_utils__WEBPACK_IMPORTED_MODULE_7__.stripInternalSearchParams)(normalizeURL).toString(),\n        init: {\n            body: params.request.body,\n            headers: requestHeaders,\n            method: params.request.method,\n            nextConfig: params.request.nextConfig,\n            signal: params.request.signal\n        }\n    });\n    /**\n   * This allows to identify the request as a data request. The user doesn't\n   * need to know about this property neither use it. We add it for testing\n   * purposes.\n   */ if (isNextDataRequest) {\n        Object.defineProperty(request, '__isData', {\n            enumerable: false,\n            value: true\n        });\n    }\n    if (!globalThis.__incrementalCache && params.IncrementalCache) {\n        ;\n        globalThis.__incrementalCache = new params.IncrementalCache({\n            appDir: true,\n            fetchCache: true,\n            minimalMode: \"development\" !== 'development',\n            fetchCacheKeyPrefix: \"\",\n            dev: \"development\" === 'development',\n            requestHeaders: params.request.headers,\n            requestProtocol: 'https',\n            getPrerenderManifest: ()=>{\n                return {\n                    version: -1,\n                    routes: {},\n                    dynamicRoutes: {},\n                    notFoundRoutes: [],\n                    preview: (0,_get_edge_preview_props__WEBPACK_IMPORTED_MODULE_18__.getEdgePreviewProps)()\n                };\n            }\n        });\n    }\n    // if we're in an edge runtime sandbox, we should use the waitUntil\n    // that we receive from the enclosing NextServer\n    const outerWaitUntil = params.request.waitUntil ?? ((_getBuiltinRequestContext = (0,_after_builtin_request_context__WEBPACK_IMPORTED_MODULE_19__.getBuiltinRequestContext)()) == null ? void 0 : _getBuiltinRequestContext.waitUntil);\n    const event = new _spec_extension_fetch_event__WEBPACK_IMPORTED_MODULE_2__.NextFetchEvent({\n        request,\n        page: params.page,\n        context: outerWaitUntil ? {\n            waitUntil: outerWaitUntil\n        } : undefined\n    });\n    let response;\n    let cookiesFromResponse;\n    response = await propagator(request, ()=>{\n        // we only care to make async storage available for middleware\n        const isMiddleware = params.page === '/middleware' || params.page === '/src/middleware';\n        if (isMiddleware) {\n            // if we're in an edge function, we only get a subset of `nextConfig` (no `experimental`),\n            // so we have to inject it via DefinePlugin.\n            // in `next start` this will be passed normally (see `NextNodeServer.runMiddleware`).\n            const waitUntil = event.waitUntil.bind(event);\n            const closeController = new _web_on_close__WEBPACK_IMPORTED_MODULE_17__.CloseController();\n            return (0,_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_15__.getTracer)().trace(_lib_trace_constants__WEBPACK_IMPORTED_MODULE_16__.MiddlewareSpan.execute, {\n                spanName: `middleware ${request.method} ${request.nextUrl.pathname}`,\n                attributes: {\n                    'http.target': request.nextUrl.pathname,\n                    'http.method': request.method\n                }\n            }, async ()=>{\n                try {\n                    var _params_request_nextConfig_experimental, _params_request_nextConfig, _params_request_nextConfig_experimental1, _params_request_nextConfig1;\n                    const onUpdateCookies = (cookies)=>{\n                        cookiesFromResponse = cookies;\n                    };\n                    const previewProps = (0,_get_edge_preview_props__WEBPACK_IMPORTED_MODULE_18__.getEdgePreviewProps)();\n                    const page = '/' // Fake Work\n                    ;\n                    const fallbackRouteParams = null;\n                    const implicitTags = await (0,_lib_implicit_tags__WEBPACK_IMPORTED_MODULE_20__.getImplicitTags)(page, request.nextUrl, fallbackRouteParams);\n                    const requestStore = (0,_async_storage_request_store__WEBPACK_IMPORTED_MODULE_11__.createRequestStoreForAPI)(request, request.nextUrl, implicitTags, onUpdateCookies, previewProps);\n                    const workStore = (0,_async_storage_work_store__WEBPACK_IMPORTED_MODULE_13__.createWorkStore)({\n                        page,\n                        fallbackRouteParams,\n                        renderOpts: {\n                            cacheLifeProfiles: (_params_request_nextConfig = params.request.nextConfig) == null ? void 0 : (_params_request_nextConfig_experimental = _params_request_nextConfig.experimental) == null ? void 0 : _params_request_nextConfig_experimental.cacheLife,\n                            experimental: {\n                                isRoutePPREnabled: false,\n                                dynamicIO: false,\n                                authInterrupts: !!((_params_request_nextConfig1 = params.request.nextConfig) == null ? void 0 : (_params_request_nextConfig_experimental1 = _params_request_nextConfig1.experimental) == null ? void 0 : _params_request_nextConfig_experimental1.authInterrupts)\n                            },\n                            supportsDynamicResponse: true,\n                            waitUntil,\n                            onClose: closeController.onClose.bind(closeController),\n                            onAfterTaskError: undefined\n                        },\n                        requestEndedState: {\n                            ended: false\n                        },\n                        isPrefetchRequest: request.headers.has(_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_9__.NEXT_ROUTER_PREFETCH_HEADER),\n                        buildId: buildId ?? '',\n                        previouslyRevalidatedTags: []\n                    });\n                    return await _app_render_work_async_storage_external__WEBPACK_IMPORTED_MODULE_14__.workAsyncStorage.run(workStore, ()=>_app_render_work_unit_async_storage_external__WEBPACK_IMPORTED_MODULE_12__.workUnitAsyncStorage.run(requestStore, params.handler, request, event));\n                } finally{\n                    // middleware cannot stream, so we can consider the response closed\n                    // as soon as the handler returns.\n                    // we can delay running it until a bit later --\n                    // if it's needed, we'll have a `waitUntil` lock anyway.\n                    setTimeout(()=>{\n                        closeController.dispatchClose();\n                    }, 0);\n                }\n            });\n        }\n        return params.handler(request, event);\n    });\n    // check if response is a Response object\n    if (response && !(response instanceof Response)) {\n        throw Object.defineProperty(new TypeError('Expected an instance of Response to be returned'), \"__NEXT_ERROR_CODE\", {\n            value: \"E567\",\n            enumerable: false,\n            configurable: true\n        });\n    }\n    if (response && cookiesFromResponse) {\n        response.headers.set('set-cookie', cookiesFromResponse);\n    }\n    /**\n   * For rewrites we must always include the locale in the final pathname\n   * so we re-create the NextURL forcing it to include it when the it is\n   * an internal rewrite. Also we make sure the outgoing rewrite URL is\n   * a data URL if the request was a data request.\n   */ const rewrite = response == null ? void 0 : response.headers.get('x-middleware-rewrite');\n    if (response && rewrite && (isRSCRequest || !isEdgeRendering)) {\n        const destination = new _next_url__WEBPACK_IMPORTED_MODULE_6__.NextURL(rewrite, {\n            forceLocale: true,\n            headers: params.request.headers,\n            nextConfig: params.request.nextConfig\n        });\n        if ( true && !isEdgeRendering) {\n            if (destination.host === request.nextUrl.host) {\n                destination.buildId = buildId || destination.buildId;\n                response.headers.set('x-middleware-rewrite', String(destination));\n            }\n        }\n        /**\n     * When the request is a data request we must show if there was a rewrite\n     * with an internal header so the client knows which component to load\n     * from the data request.\n     */ const { url: relativeDestination, isRelative } = (0,_shared_lib_router_utils_relativize_url__WEBPACK_IMPORTED_MODULE_5__.parseRelativeURL)(destination.toString(), requestURL.toString());\n        if (!isEdgeRendering && isNextDataRequest && // if the rewrite is external and external rewrite\n        // resolving config is enabled don't add this header\n        // so the upstream app can set it instead\n        !( false && 0)) {\n            response.headers.set('x-nextjs-rewrite', relativeDestination);\n        }\n        // If this is an RSC request, and the pathname or search has changed, and\n        // this isn't an external rewrite, we need to set the rewritten pathname and\n        // query headers.\n        if (isRSCRequest && isRelative) {\n            if (requestURL.pathname !== destination.pathname) {\n                response.headers.set(_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_9__.NEXT_REWRITTEN_PATH_HEADER, destination.pathname);\n            }\n            if (requestURL.search !== destination.search) {\n                response.headers.set(_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_9__.NEXT_REWRITTEN_QUERY_HEADER, // remove the leading ? from the search string\n                destination.search.slice(1));\n            }\n        }\n    }\n    /**\n   * For redirects we will not include the locale in case when it is the\n   * default and we must also make sure the outgoing URL is a data one if\n   * the incoming request was a data request.\n   */ const redirect = response == null ? void 0 : response.headers.get('Location');\n    if (response && redirect && !isEdgeRendering) {\n        const redirectURL = new _next_url__WEBPACK_IMPORTED_MODULE_6__.NextURL(redirect, {\n            forceLocale: false,\n            headers: params.request.headers,\n            nextConfig: params.request.nextConfig\n        });\n        /**\n     * Responses created from redirects have immutable headers so we have\n     * to clone the response to be able to modify it.\n     */ response = new Response(response.body, response);\n        if (true) {\n            if (redirectURL.host === requestURL.host) {\n                redirectURL.buildId = buildId || redirectURL.buildId;\n                response.headers.set('Location', redirectURL.toString());\n            }\n        }\n        /**\n     * When the request is a data request we can't use the location header as\n     * it may end up with CORS error. Instead we map to an internal header so\n     * the client knows the destination.\n     */ if (isNextDataRequest) {\n            response.headers.delete('Location');\n            response.headers.set('x-nextjs-redirect', (0,_shared_lib_router_utils_relativize_url__WEBPACK_IMPORTED_MODULE_5__.getRelativeURL)(redirectURL.toString(), requestURL.toString()));\n        }\n    }\n    const finalResponse = response ? response : _spec_extension_response__WEBPACK_IMPORTED_MODULE_4__.NextResponse.next();\n    // Flight headers are not overridable / removable so they are applied at the end.\n    const middlewareOverrideHeaders = finalResponse.headers.get('x-middleware-override-headers');\n    const overwrittenHeaders = [];\n    if (middlewareOverrideHeaders) {\n        for (const [key, value] of flightHeaders){\n            finalResponse.headers.set(`x-middleware-request-${key}`, value);\n            overwrittenHeaders.push(key);\n        }\n        if (overwrittenHeaders.length > 0) {\n            finalResponse.headers.set('x-middleware-override-headers', middlewareOverrideHeaders + ',' + overwrittenHeaders.join(','));\n        }\n    }\n    return {\n        response: finalResponse,\n        waitUntil: (0,_spec_extension_fetch_event__WEBPACK_IMPORTED_MODULE_2__.getWaitUntilPromiseFromEvent)(event) ?? Promise.resolve(),\n        fetchMetrics: request.fetchMetrics\n    };\n}\n\n//# sourceMappingURL=adapter.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKG1pZGRsZXdhcmUpLy4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9lc20vc2VydmVyL3dlYi9hZGFwdGVyLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQTZDO0FBQ2tDO0FBQ2E7QUFDckM7QUFDRTtBQUN1QztBQUMzRDtBQUN5QjtBQUNZO0FBQ3VFO0FBQ3JGO0FBQ2M7QUFDWTtBQUN4QjtBQUNlO0FBQ1k7QUFDekM7QUFDUTtBQUNQO0FBQ2M7QUFDYTtBQUNyQjtBQUNoRCw4QkFBOEIsZ0VBQVc7QUFDaEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHdDQUF3QyxzREFBa0I7QUFDMUQ7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQSx3Q0FBd0Msc0RBQWtCO0FBQzFEO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0Esd0NBQXdDLHNEQUFrQjtBQUMxRDtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxtQkFBbUIsNkRBQVM7QUFDNUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvQkFBb0Isd0NBQXdDLEVBQUUsbUJBQU8sQ0FBQywrSEFBNkM7QUFDbkg7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBLFVBQVUsMEVBQStCO0FBQ3pDO0FBQ0E7QUFDQSx5QkFBeUIsbUZBQWU7QUFDeEMsMkJBQTJCLDhDQUFPO0FBQ2xDO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw4QkFBOEIsK0RBQXVCO0FBQ3JEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwyQkFBMkIsbUVBQTJCO0FBQ3REO0FBQ0EsNENBQTRDLDZFQUFVO0FBQ3REO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDZCQUE2QixpRkFBYztBQUMzQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EseUJBQXlCLE1BQThDLEdBQUcsQ0FBMkI7QUFDckc7QUFDQTtBQUNBO0FBQ0EsZUFBZSwwRUFBeUI7QUFDeEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx5QkFBeUIsYUFBb0I7QUFDN0MsaUNBQWlDLEVBQXlDO0FBQzFFLGlCQUFpQixhQUFvQjtBQUNyQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsOEJBQThCO0FBQzlCLHFDQUFxQztBQUNyQztBQUNBLDZCQUE2Qiw2RUFBbUI7QUFDaEQ7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQSxxRkFBcUYseUZBQXdCO0FBQzdHLHNCQUFzQix1RUFBYztBQUNwQztBQUNBO0FBQ0E7QUFDQTtBQUNBLFVBQVU7QUFDVixLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx3Q0FBd0MsMkRBQWU7QUFDdkQsbUJBQW1CLDZEQUFTLFNBQVMsaUVBQWM7QUFDbkQsd0NBQXdDLGdCQUFnQixFQUFFLHlCQUF5QjtBQUNuRjtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EseUNBQXlDLDZFQUFtQjtBQUM1RDtBQUNBO0FBQ0E7QUFDQSwrQ0FBK0Msb0VBQWU7QUFDOUQseUNBQXlDLHVGQUF3QjtBQUNqRSxzQ0FBc0MsMkVBQWU7QUFDckQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDZCQUE2QjtBQUM3QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLHlCQUF5QjtBQUN6QjtBQUNBO0FBQ0EseUJBQXlCO0FBQ3pCLCtEQUErRCw4RkFBMkI7QUFDMUY7QUFDQTtBQUNBLHFCQUFxQjtBQUNyQixpQ0FBaUMsc0ZBQWdCLG9CQUFvQiwrRkFBb0I7QUFDekYsa0JBQWtCO0FBQ2xCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFCQUFxQjtBQUNyQjtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0NBQWdDLDhDQUFPO0FBQ3ZDO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVCxZQUFZLEtBQStDO0FBQzNEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQix1Q0FBdUMsRUFBRSx5RkFBZ0I7QUFDekU7QUFDQTtBQUNBO0FBQ0EsVUFBVSxNQUFzRCxJQUFJLENBQTBDO0FBQzlHO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUNBQXFDLDZGQUEwQjtBQUMvRDtBQUNBO0FBQ0EscUNBQXFDLDhGQUEyQjtBQUNoRTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdDQUFnQyw4Q0FBTztBQUN2QztBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZLElBQStDO0FBQzNEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzREFBc0QsdUZBQWM7QUFDcEU7QUFDQTtBQUNBLGdEQUFnRCxrRUFBWTtBQUM1RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsOERBQThELElBQUk7QUFDbEU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG1CQUFtQix5RkFBNEI7QUFDL0M7QUFDQTtBQUNBOztBQUVBIiwic291cmNlcyI6WyJDOlxcUm9LZXkgQXBwXFxyb2tleS1hcHBcXG5vZGVfbW9kdWxlc1xcbmV4dFxcZGlzdFxcZXNtXFxzZXJ2ZXJcXHdlYlxcYWRhcHRlci5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQYWdlU2lnbmF0dXJlRXJyb3IgfSBmcm9tICcuL2Vycm9yJztcbmltcG9ydCB7IGZyb21Ob2RlT3V0Z29pbmdIdHRwSGVhZGVycywgbm9ybWFsaXplTmV4dFF1ZXJ5UGFyYW0gfSBmcm9tICcuL3V0aWxzJztcbmltcG9ydCB7IE5leHRGZXRjaEV2ZW50LCBnZXRXYWl0VW50aWxQcm9taXNlRnJvbUV2ZW50IH0gZnJvbSAnLi9zcGVjLWV4dGVuc2lvbi9mZXRjaC1ldmVudCc7XG5pbXBvcnQgeyBOZXh0UmVxdWVzdCB9IGZyb20gJy4vc3BlYy1leHRlbnNpb24vcmVxdWVzdCc7XG5pbXBvcnQgeyBOZXh0UmVzcG9uc2UgfSBmcm9tICcuL3NwZWMtZXh0ZW5zaW9uL3Jlc3BvbnNlJztcbmltcG9ydCB7IHBhcnNlUmVsYXRpdmVVUkwsIGdldFJlbGF0aXZlVVJMIH0gZnJvbSAnLi4vLi4vc2hhcmVkL2xpYi9yb3V0ZXIvdXRpbHMvcmVsYXRpdml6ZS11cmwnO1xuaW1wb3J0IHsgTmV4dFVSTCB9IGZyb20gJy4vbmV4dC11cmwnO1xuaW1wb3J0IHsgc3RyaXBJbnRlcm5hbFNlYXJjaFBhcmFtcyB9IGZyb20gJy4uL2ludGVybmFsLXV0aWxzJztcbmltcG9ydCB7IG5vcm1hbGl6ZVJzY1VSTCB9IGZyb20gJy4uLy4uL3NoYXJlZC9saWIvcm91dGVyL3V0aWxzL2FwcC1wYXRocyc7XG5pbXBvcnQgeyBGTElHSFRfSEVBREVSUywgTkVYVF9SRVdSSVRURU5fUEFUSF9IRUFERVIsIE5FWFRfUkVXUklUVEVOX1FVRVJZX0hFQURFUiwgUlNDX0hFQURFUiB9IGZyb20gJy4uLy4uL2NsaWVudC9jb21wb25lbnRzL2FwcC1yb3V0ZXItaGVhZGVycyc7XG5pbXBvcnQgeyBlbnN1cmVJbnN0cnVtZW50YXRpb25SZWdpc3RlcmVkIH0gZnJvbSAnLi9nbG9iYWxzJztcbmltcG9ydCB7IGNyZWF0ZVJlcXVlc3RTdG9yZUZvckFQSSB9IGZyb20gJy4uL2FzeW5jLXN0b3JhZ2UvcmVxdWVzdC1zdG9yZSc7XG5pbXBvcnQgeyB3b3JrVW5pdEFzeW5jU3RvcmFnZSB9IGZyb20gJy4uL2FwcC1yZW5kZXIvd29yay11bml0LWFzeW5jLXN0b3JhZ2UuZXh0ZXJuYWwnO1xuaW1wb3J0IHsgY3JlYXRlV29ya1N0b3JlIH0gZnJvbSAnLi4vYXN5bmMtc3RvcmFnZS93b3JrLXN0b3JlJztcbmltcG9ydCB7IHdvcmtBc3luY1N0b3JhZ2UgfSBmcm9tICcuLi9hcHAtcmVuZGVyL3dvcmstYXN5bmMtc3RvcmFnZS5leHRlcm5hbCc7XG5pbXBvcnQgeyBORVhUX1JPVVRFUl9QUkVGRVRDSF9IRUFERVIgfSBmcm9tICcuLi8uLi9jbGllbnQvY29tcG9uZW50cy9hcHAtcm91dGVyLWhlYWRlcnMnO1xuaW1wb3J0IHsgZ2V0VHJhY2VyIH0gZnJvbSAnLi4vbGliL3RyYWNlL3RyYWNlcic7XG5pbXBvcnQgeyBNaWRkbGV3YXJlU3BhbiB9IGZyb20gJy4uL2xpYi90cmFjZS9jb25zdGFudHMnO1xuaW1wb3J0IHsgQ2xvc2VDb250cm9sbGVyIH0gZnJvbSAnLi93ZWItb24tY2xvc2UnO1xuaW1wb3J0IHsgZ2V0RWRnZVByZXZpZXdQcm9wcyB9IGZyb20gJy4vZ2V0LWVkZ2UtcHJldmlldy1wcm9wcyc7XG5pbXBvcnQgeyBnZXRCdWlsdGluUmVxdWVzdENvbnRleHQgfSBmcm9tICcuLi9hZnRlci9idWlsdGluLXJlcXVlc3QtY29udGV4dCc7XG5pbXBvcnQgeyBnZXRJbXBsaWNpdFRhZ3MgfSBmcm9tICcuLi9saWIvaW1wbGljaXQtdGFncyc7XG5leHBvcnQgY2xhc3MgTmV4dFJlcXVlc3RIaW50IGV4dGVuZHMgTmV4dFJlcXVlc3Qge1xuICAgIGNvbnN0cnVjdG9yKHBhcmFtcyl7XG4gICAgICAgIHN1cGVyKHBhcmFtcy5pbnB1dCwgcGFyYW1zLmluaXQpO1xuICAgICAgICB0aGlzLnNvdXJjZVBhZ2UgPSBwYXJhbXMucGFnZTtcbiAgICB9XG4gICAgZ2V0IHJlcXVlc3QoKSB7XG4gICAgICAgIHRocm93IE9iamVjdC5kZWZpbmVQcm9wZXJ0eShuZXcgUGFnZVNpZ25hdHVyZUVycm9yKHtcbiAgICAgICAgICAgIHBhZ2U6IHRoaXMuc291cmNlUGFnZVxuICAgICAgICB9KSwgXCJfX05FWFRfRVJST1JfQ09ERVwiLCB7XG4gICAgICAgICAgICB2YWx1ZTogXCJFMzk0XCIsXG4gICAgICAgICAgICBlbnVtZXJhYmxlOiBmYWxzZSxcbiAgICAgICAgICAgIGNvbmZpZ3VyYWJsZTogdHJ1ZVxuICAgICAgICB9KTtcbiAgICB9XG4gICAgcmVzcG9uZFdpdGgoKSB7XG4gICAgICAgIHRocm93IE9iamVjdC5kZWZpbmVQcm9wZXJ0eShuZXcgUGFnZVNpZ25hdHVyZUVycm9yKHtcbiAgICAgICAgICAgIHBhZ2U6IHRoaXMuc291cmNlUGFnZVxuICAgICAgICB9KSwgXCJfX05FWFRfRVJST1JfQ09ERVwiLCB7XG4gICAgICAgICAgICB2YWx1ZTogXCJFMzk0XCIsXG4gICAgICAgICAgICBlbnVtZXJhYmxlOiBmYWxzZSxcbiAgICAgICAgICAgIGNvbmZpZ3VyYWJsZTogdHJ1ZVxuICAgICAgICB9KTtcbiAgICB9XG4gICAgd2FpdFVudGlsKCkge1xuICAgICAgICB0aHJvdyBPYmplY3QuZGVmaW5lUHJvcGVydHkobmV3IFBhZ2VTaWduYXR1cmVFcnJvcih7XG4gICAgICAgICAgICBwYWdlOiB0aGlzLnNvdXJjZVBhZ2VcbiAgICAgICAgfSksIFwiX19ORVhUX0VSUk9SX0NPREVcIiwge1xuICAgICAgICAgICAgdmFsdWU6IFwiRTM5NFwiLFxuICAgICAgICAgICAgZW51bWVyYWJsZTogZmFsc2UsXG4gICAgICAgICAgICBjb25maWd1cmFibGU6IHRydWVcbiAgICAgICAgfSk7XG4gICAgfVxufVxuY29uc3QgaGVhZGVyc0dldHRlciA9IHtcbiAgICBrZXlzOiAoaGVhZGVycyk9PkFycmF5LmZyb20oaGVhZGVycy5rZXlzKCkpLFxuICAgIGdldDogKGhlYWRlcnMsIGtleSk9PmhlYWRlcnMuZ2V0KGtleSkgPz8gdW5kZWZpbmVkXG59O1xubGV0IHByb3BhZ2F0b3IgPSAocmVxdWVzdCwgZm4pPT57XG4gICAgY29uc3QgdHJhY2VyID0gZ2V0VHJhY2VyKCk7XG4gICAgcmV0dXJuIHRyYWNlci53aXRoUHJvcGFnYXRlZENvbnRleHQocmVxdWVzdC5oZWFkZXJzLCBmbiwgaGVhZGVyc0dldHRlcik7XG59O1xubGV0IHRlc3RBcGlzSW50ZXJjZXB0ZWQgPSBmYWxzZTtcbmZ1bmN0aW9uIGVuc3VyZVRlc3RBcGlzSW50ZXJjZXB0ZWQoKSB7XG4gICAgaWYgKCF0ZXN0QXBpc0ludGVyY2VwdGVkKSB7XG4gICAgICAgIHRlc3RBcGlzSW50ZXJjZXB0ZWQgPSB0cnVlO1xuICAgICAgICBpZiAocHJvY2Vzcy5lbnYuTkVYVF9QUklWQVRFX1RFU1RfUFJPWFkgPT09ICd0cnVlJykge1xuICAgICAgICAgICAgY29uc3QgeyBpbnRlcmNlcHRUZXN0QXBpcywgd3JhcFJlcXVlc3RIYW5kbGVyIH0gPSByZXF1aXJlKCduZXh0L2Rpc3QvZXhwZXJpbWVudGFsL3Rlc3Rtb2RlL3NlcnZlci1lZGdlJyk7XG4gICAgICAgICAgICBpbnRlcmNlcHRUZXN0QXBpcygpO1xuICAgICAgICAgICAgcHJvcGFnYXRvciA9IHdyYXBSZXF1ZXN0SGFuZGxlcihwcm9wYWdhdG9yKTtcbiAgICAgICAgfVxuICAgIH1cbn1cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBhZGFwdGVyKHBhcmFtcykge1xuICAgIHZhciBfZ2V0QnVpbHRpblJlcXVlc3RDb250ZXh0O1xuICAgIGVuc3VyZVRlc3RBcGlzSW50ZXJjZXB0ZWQoKTtcbiAgICBhd2FpdCBlbnN1cmVJbnN0cnVtZW50YXRpb25SZWdpc3RlcmVkKCk7XG4gICAgLy8gVE9ETy1BUFA6IHVzZSBleHBsaWNpdCBtYXJrZXIgZm9yIHRoaXNcbiAgICBjb25zdCBpc0VkZ2VSZW5kZXJpbmcgPSB0eXBlb2YgZ2xvYmFsVGhpcy5fX0JVSUxEX01BTklGRVNUICE9PSAndW5kZWZpbmVkJztcbiAgICBwYXJhbXMucmVxdWVzdC51cmwgPSBub3JtYWxpemVSc2NVUkwocGFyYW1zLnJlcXVlc3QudXJsKTtcbiAgICBjb25zdCByZXF1ZXN0VVJMID0gbmV3IE5leHRVUkwocGFyYW1zLnJlcXVlc3QudXJsLCB7XG4gICAgICAgIGhlYWRlcnM6IHBhcmFtcy5yZXF1ZXN0LmhlYWRlcnMsXG4gICAgICAgIG5leHRDb25maWc6IHBhcmFtcy5yZXF1ZXN0Lm5leHRDb25maWdcbiAgICB9KTtcbiAgICAvLyBJdGVyYXRvciB1c2VzIGFuIGluZGV4IHRvIGtlZXAgdHJhY2sgb2YgdGhlIGN1cnJlbnQgaXRlcmF0aW9uLiBCZWNhdXNlIG9mIGRlbGV0aW5nIGFuZCBhcHBlbmRpbmcgYmVsb3cgd2UgY2FuJ3QganVzdCB1c2UgdGhlIGl0ZXJhdG9yLlxuICAgIC8vIEluc3RlYWQgd2UgdXNlIHRoZSBrZXlzIGJlZm9yZSBpdGVyYXRpb24uXG4gICAgY29uc3Qga2V5cyA9IFtcbiAgICAgICAgLi4ucmVxdWVzdFVSTC5zZWFyY2hQYXJhbXMua2V5cygpXG4gICAgXTtcbiAgICBmb3IgKGNvbnN0IGtleSBvZiBrZXlzKXtcbiAgICAgICAgY29uc3QgdmFsdWUgPSByZXF1ZXN0VVJMLnNlYXJjaFBhcmFtcy5nZXRBbGwoa2V5KTtcbiAgICAgICAgY29uc3Qgbm9ybWFsaXplZEtleSA9IG5vcm1hbGl6ZU5leHRRdWVyeVBhcmFtKGtleSk7XG4gICAgICAgIGlmIChub3JtYWxpemVkS2V5KSB7XG4gICAgICAgICAgICByZXF1ZXN0VVJMLnNlYXJjaFBhcmFtcy5kZWxldGUobm9ybWFsaXplZEtleSk7XG4gICAgICAgICAgICBmb3IgKGNvbnN0IHZhbCBvZiB2YWx1ZSl7XG4gICAgICAgICAgICAgICAgcmVxdWVzdFVSTC5zZWFyY2hQYXJhbXMuYXBwZW5kKG5vcm1hbGl6ZWRLZXksIHZhbCk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICByZXF1ZXN0VVJMLnNlYXJjaFBhcmFtcy5kZWxldGUoa2V5KTtcbiAgICAgICAgfVxuICAgIH1cbiAgICAvLyBFbnN1cmUgdXNlcnMgb25seSBzZWUgcGFnZSByZXF1ZXN0cywgbmV2ZXIgZGF0YSByZXF1ZXN0cy5cbiAgICBjb25zdCBidWlsZElkID0gcmVxdWVzdFVSTC5idWlsZElkO1xuICAgIHJlcXVlc3RVUkwuYnVpbGRJZCA9ICcnO1xuICAgIGNvbnN0IHJlcXVlc3RIZWFkZXJzID0gZnJvbU5vZGVPdXRnb2luZ0h0dHBIZWFkZXJzKHBhcmFtcy5yZXF1ZXN0LmhlYWRlcnMpO1xuICAgIGNvbnN0IGlzTmV4dERhdGFSZXF1ZXN0ID0gcmVxdWVzdEhlYWRlcnMuaGFzKCd4LW5leHRqcy1kYXRhJyk7XG4gICAgY29uc3QgaXNSU0NSZXF1ZXN0ID0gcmVxdWVzdEhlYWRlcnMuZ2V0KFJTQ19IRUFERVIpID09PSAnMSc7XG4gICAgaWYgKGlzTmV4dERhdGFSZXF1ZXN0ICYmIHJlcXVlc3RVUkwucGF0aG5hbWUgPT09ICcvaW5kZXgnKSB7XG4gICAgICAgIHJlcXVlc3RVUkwucGF0aG5hbWUgPSAnLyc7XG4gICAgfVxuICAgIGNvbnN0IGZsaWdodEhlYWRlcnMgPSBuZXcgTWFwKCk7XG4gICAgLy8gSGVhZGVycyBzaG91bGQgb25seSBiZSBzdHJpcHBlZCBmb3IgbWlkZGxld2FyZVxuICAgIGlmICghaXNFZGdlUmVuZGVyaW5nKSB7XG4gICAgICAgIGZvciAoY29uc3QgaGVhZGVyIG9mIEZMSUdIVF9IRUFERVJTKXtcbiAgICAgICAgICAgIGNvbnN0IGtleSA9IGhlYWRlci50b0xvd2VyQ2FzZSgpO1xuICAgICAgICAgICAgY29uc3QgdmFsdWUgPSByZXF1ZXN0SGVhZGVycy5nZXQoa2V5KTtcbiAgICAgICAgICAgIGlmICh2YWx1ZSAhPT0gbnVsbCkge1xuICAgICAgICAgICAgICAgIGZsaWdodEhlYWRlcnMuc2V0KGtleSwgdmFsdWUpO1xuICAgICAgICAgICAgICAgIHJlcXVlc3RIZWFkZXJzLmRlbGV0ZShrZXkpO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgfVxuICAgIGNvbnN0IG5vcm1hbGl6ZVVSTCA9IHByb2Nlc3MuZW52Ll9fTkVYVF9OT19NSURETEVXQVJFX1VSTF9OT1JNQUxJWkUgPyBuZXcgVVJMKHBhcmFtcy5yZXF1ZXN0LnVybCkgOiByZXF1ZXN0VVJMO1xuICAgIGNvbnN0IHJlcXVlc3QgPSBuZXcgTmV4dFJlcXVlc3RIaW50KHtcbiAgICAgICAgcGFnZTogcGFyYW1zLnBhZ2UsXG4gICAgICAgIC8vIFN0cmlwIGludGVybmFsIHF1ZXJ5IHBhcmFtZXRlcnMgb2ZmIHRoZSByZXF1ZXN0LlxuICAgICAgICBpbnB1dDogc3RyaXBJbnRlcm5hbFNlYXJjaFBhcmFtcyhub3JtYWxpemVVUkwpLnRvU3RyaW5nKCksXG4gICAgICAgIGluaXQ6IHtcbiAgICAgICAgICAgIGJvZHk6IHBhcmFtcy5yZXF1ZXN0LmJvZHksXG4gICAgICAgICAgICBoZWFkZXJzOiByZXF1ZXN0SGVhZGVycyxcbiAgICAgICAgICAgIG1ldGhvZDogcGFyYW1zLnJlcXVlc3QubWV0aG9kLFxuICAgICAgICAgICAgbmV4dENvbmZpZzogcGFyYW1zLnJlcXVlc3QubmV4dENvbmZpZyxcbiAgICAgICAgICAgIHNpZ25hbDogcGFyYW1zLnJlcXVlc3Quc2lnbmFsXG4gICAgICAgIH1cbiAgICB9KTtcbiAgICAvKipcbiAgICogVGhpcyBhbGxvd3MgdG8gaWRlbnRpZnkgdGhlIHJlcXVlc3QgYXMgYSBkYXRhIHJlcXVlc3QuIFRoZSB1c2VyIGRvZXNuJ3RcbiAgICogbmVlZCB0byBrbm93IGFib3V0IHRoaXMgcHJvcGVydHkgbmVpdGhlciB1c2UgaXQuIFdlIGFkZCBpdCBmb3IgdGVzdGluZ1xuICAgKiBwdXJwb3Nlcy5cbiAgICovIGlmIChpc05leHREYXRhUmVxdWVzdCkge1xuICAgICAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkocmVxdWVzdCwgJ19faXNEYXRhJywge1xuICAgICAgICAgICAgZW51bWVyYWJsZTogZmFsc2UsXG4gICAgICAgICAgICB2YWx1ZTogdHJ1ZVxuICAgICAgICB9KTtcbiAgICB9XG4gICAgaWYgKCFnbG9iYWxUaGlzLl9faW5jcmVtZW50YWxDYWNoZSAmJiBwYXJhbXMuSW5jcmVtZW50YWxDYWNoZSkge1xuICAgICAgICA7XG4gICAgICAgIGdsb2JhbFRoaXMuX19pbmNyZW1lbnRhbENhY2hlID0gbmV3IHBhcmFtcy5JbmNyZW1lbnRhbENhY2hlKHtcbiAgICAgICAgICAgIGFwcERpcjogdHJ1ZSxcbiAgICAgICAgICAgIGZldGNoQ2FjaGU6IHRydWUsXG4gICAgICAgICAgICBtaW5pbWFsTW9kZTogcHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdkZXZlbG9wbWVudCcsXG4gICAgICAgICAgICBmZXRjaENhY2hlS2V5UHJlZml4OiBwcm9jZXNzLmVudi5fX05FWFRfRkVUQ0hfQ0FDSEVfS0VZX1BSRUZJWCxcbiAgICAgICAgICAgIGRldjogcHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09ICdkZXZlbG9wbWVudCcsXG4gICAgICAgICAgICByZXF1ZXN0SGVhZGVyczogcGFyYW1zLnJlcXVlc3QuaGVhZGVycyxcbiAgICAgICAgICAgIHJlcXVlc3RQcm90b2NvbDogJ2h0dHBzJyxcbiAgICAgICAgICAgIGdldFByZXJlbmRlck1hbmlmZXN0OiAoKT0+e1xuICAgICAgICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICAgICAgICAgIHZlcnNpb246IC0xLFxuICAgICAgICAgICAgICAgICAgICByb3V0ZXM6IHt9LFxuICAgICAgICAgICAgICAgICAgICBkeW5hbWljUm91dGVzOiB7fSxcbiAgICAgICAgICAgICAgICAgICAgbm90Rm91bmRSb3V0ZXM6IFtdLFxuICAgICAgICAgICAgICAgICAgICBwcmV2aWV3OiBnZXRFZGdlUHJldmlld1Byb3BzKClcbiAgICAgICAgICAgICAgICB9O1xuICAgICAgICAgICAgfVxuICAgICAgICB9KTtcbiAgICB9XG4gICAgLy8gaWYgd2UncmUgaW4gYW4gZWRnZSBydW50aW1lIHNhbmRib3gsIHdlIHNob3VsZCB1c2UgdGhlIHdhaXRVbnRpbFxuICAgIC8vIHRoYXQgd2UgcmVjZWl2ZSBmcm9tIHRoZSBlbmNsb3NpbmcgTmV4dFNlcnZlclxuICAgIGNvbnN0IG91dGVyV2FpdFVudGlsID0gcGFyYW1zLnJlcXVlc3Qud2FpdFVudGlsID8/ICgoX2dldEJ1aWx0aW5SZXF1ZXN0Q29udGV4dCA9IGdldEJ1aWx0aW5SZXF1ZXN0Q29udGV4dCgpKSA9PSBudWxsID8gdm9pZCAwIDogX2dldEJ1aWx0aW5SZXF1ZXN0Q29udGV4dC53YWl0VW50aWwpO1xuICAgIGNvbnN0IGV2ZW50ID0gbmV3IE5leHRGZXRjaEV2ZW50KHtcbiAgICAgICAgcmVxdWVzdCxcbiAgICAgICAgcGFnZTogcGFyYW1zLnBhZ2UsXG4gICAgICAgIGNvbnRleHQ6IG91dGVyV2FpdFVudGlsID8ge1xuICAgICAgICAgICAgd2FpdFVudGlsOiBvdXRlcldhaXRVbnRpbFxuICAgICAgICB9IDogdW5kZWZpbmVkXG4gICAgfSk7XG4gICAgbGV0IHJlc3BvbnNlO1xuICAgIGxldCBjb29raWVzRnJvbVJlc3BvbnNlO1xuICAgIHJlc3BvbnNlID0gYXdhaXQgcHJvcGFnYXRvcihyZXF1ZXN0LCAoKT0+e1xuICAgICAgICAvLyB3ZSBvbmx5IGNhcmUgdG8gbWFrZSBhc3luYyBzdG9yYWdlIGF2YWlsYWJsZSBmb3IgbWlkZGxld2FyZVxuICAgICAgICBjb25zdCBpc01pZGRsZXdhcmUgPSBwYXJhbXMucGFnZSA9PT0gJy9taWRkbGV3YXJlJyB8fCBwYXJhbXMucGFnZSA9PT0gJy9zcmMvbWlkZGxld2FyZSc7XG4gICAgICAgIGlmIChpc01pZGRsZXdhcmUpIHtcbiAgICAgICAgICAgIC8vIGlmIHdlJ3JlIGluIGFuIGVkZ2UgZnVuY3Rpb24sIHdlIG9ubHkgZ2V0IGEgc3Vic2V0IG9mIGBuZXh0Q29uZmlnYCAobm8gYGV4cGVyaW1lbnRhbGApLFxuICAgICAgICAgICAgLy8gc28gd2UgaGF2ZSB0byBpbmplY3QgaXQgdmlhIERlZmluZVBsdWdpbi5cbiAgICAgICAgICAgIC8vIGluIGBuZXh0IHN0YXJ0YCB0aGlzIHdpbGwgYmUgcGFzc2VkIG5vcm1hbGx5IChzZWUgYE5leHROb2RlU2VydmVyLnJ1bk1pZGRsZXdhcmVgKS5cbiAgICAgICAgICAgIGNvbnN0IHdhaXRVbnRpbCA9IGV2ZW50LndhaXRVbnRpbC5iaW5kKGV2ZW50KTtcbiAgICAgICAgICAgIGNvbnN0IGNsb3NlQ29udHJvbGxlciA9IG5ldyBDbG9zZUNvbnRyb2xsZXIoKTtcbiAgICAgICAgICAgIHJldHVybiBnZXRUcmFjZXIoKS50cmFjZShNaWRkbGV3YXJlU3Bhbi5leGVjdXRlLCB7XG4gICAgICAgICAgICAgICAgc3Bhbk5hbWU6IGBtaWRkbGV3YXJlICR7cmVxdWVzdC5tZXRob2R9ICR7cmVxdWVzdC5uZXh0VXJsLnBhdGhuYW1lfWAsXG4gICAgICAgICAgICAgICAgYXR0cmlidXRlczoge1xuICAgICAgICAgICAgICAgICAgICAnaHR0cC50YXJnZXQnOiByZXF1ZXN0Lm5leHRVcmwucGF0aG5hbWUsXG4gICAgICAgICAgICAgICAgICAgICdodHRwLm1ldGhvZCc6IHJlcXVlc3QubWV0aG9kXG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfSwgYXN5bmMgKCk9PntcbiAgICAgICAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgICAgICAgICB2YXIgX3BhcmFtc19yZXF1ZXN0X25leHRDb25maWdfZXhwZXJpbWVudGFsLCBfcGFyYW1zX3JlcXVlc3RfbmV4dENvbmZpZywgX3BhcmFtc19yZXF1ZXN0X25leHRDb25maWdfZXhwZXJpbWVudGFsMSwgX3BhcmFtc19yZXF1ZXN0X25leHRDb25maWcxO1xuICAgICAgICAgICAgICAgICAgICBjb25zdCBvblVwZGF0ZUNvb2tpZXMgPSAoY29va2llcyk9PntcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvb2tpZXNGcm9tUmVzcG9uc2UgPSBjb29raWVzO1xuICAgICAgICAgICAgICAgICAgICB9O1xuICAgICAgICAgICAgICAgICAgICBjb25zdCBwcmV2aWV3UHJvcHMgPSBnZXRFZGdlUHJldmlld1Byb3BzKCk7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IHBhZ2UgPSAnLycgLy8gRmFrZSBXb3JrXG4gICAgICAgICAgICAgICAgICAgIDtcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgZmFsbGJhY2tSb3V0ZVBhcmFtcyA9IG51bGw7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGltcGxpY2l0VGFncyA9IGF3YWl0IGdldEltcGxpY2l0VGFncyhwYWdlLCByZXF1ZXN0Lm5leHRVcmwsIGZhbGxiYWNrUm91dGVQYXJhbXMpO1xuICAgICAgICAgICAgICAgICAgICBjb25zdCByZXF1ZXN0U3RvcmUgPSBjcmVhdGVSZXF1ZXN0U3RvcmVGb3JBUEkocmVxdWVzdCwgcmVxdWVzdC5uZXh0VXJsLCBpbXBsaWNpdFRhZ3MsIG9uVXBkYXRlQ29va2llcywgcHJldmlld1Byb3BzKTtcbiAgICAgICAgICAgICAgICAgICAgY29uc3Qgd29ya1N0b3JlID0gY3JlYXRlV29ya1N0b3JlKHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHBhZ2UsXG4gICAgICAgICAgICAgICAgICAgICAgICBmYWxsYmFja1JvdXRlUGFyYW1zLFxuICAgICAgICAgICAgICAgICAgICAgICAgcmVuZGVyT3B0czoge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNhY2hlTGlmZVByb2ZpbGVzOiAoX3BhcmFtc19yZXF1ZXN0X25leHRDb25maWcgPSBwYXJhbXMucmVxdWVzdC5uZXh0Q29uZmlnKSA9PSBudWxsID8gdm9pZCAwIDogKF9wYXJhbXNfcmVxdWVzdF9uZXh0Q29uZmlnX2V4cGVyaW1lbnRhbCA9IF9wYXJhbXNfcmVxdWVzdF9uZXh0Q29uZmlnLmV4cGVyaW1lbnRhbCkgPT0gbnVsbCA/IHZvaWQgMCA6IF9wYXJhbXNfcmVxdWVzdF9uZXh0Q29uZmlnX2V4cGVyaW1lbnRhbC5jYWNoZUxpZmUsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZXhwZXJpbWVudGFsOiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlzUm91dGVQUFJFbmFibGVkOiBmYWxzZSxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZHluYW1pY0lPOiBmYWxzZSxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYXV0aEludGVycnVwdHM6ICEhKChfcGFyYW1zX3JlcXVlc3RfbmV4dENvbmZpZzEgPSBwYXJhbXMucmVxdWVzdC5uZXh0Q29uZmlnKSA9PSBudWxsID8gdm9pZCAwIDogKF9wYXJhbXNfcmVxdWVzdF9uZXh0Q29uZmlnX2V4cGVyaW1lbnRhbDEgPSBfcGFyYW1zX3JlcXVlc3RfbmV4dENvbmZpZzEuZXhwZXJpbWVudGFsKSA9PSBudWxsID8gdm9pZCAwIDogX3BhcmFtc19yZXF1ZXN0X25leHRDb25maWdfZXhwZXJpbWVudGFsMS5hdXRoSW50ZXJydXB0cylcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9LFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN1cHBvcnRzRHluYW1pY1Jlc3BvbnNlOiB0cnVlLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHdhaXRVbnRpbCxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsb3NlOiBjbG9zZUNvbnRyb2xsZXIub25DbG9zZS5iaW5kKGNsb3NlQ29udHJvbGxlciksXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgb25BZnRlclRhc2tFcnJvcjogdW5kZWZpbmVkXG4gICAgICAgICAgICAgICAgICAgICAgICB9LFxuICAgICAgICAgICAgICAgICAgICAgICAgcmVxdWVzdEVuZGVkU3RhdGU6IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBlbmRlZDogZmFsc2VcbiAgICAgICAgICAgICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICAgICAgICAgICAgICBpc1ByZWZldGNoUmVxdWVzdDogcmVxdWVzdC5oZWFkZXJzLmhhcyhORVhUX1JPVVRFUl9QUkVGRVRDSF9IRUFERVIpLFxuICAgICAgICAgICAgICAgICAgICAgICAgYnVpbGRJZDogYnVpbGRJZCA/PyAnJyxcbiAgICAgICAgICAgICAgICAgICAgICAgIHByZXZpb3VzbHlSZXZhbGlkYXRlZFRhZ3M6IFtdXG4gICAgICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgICAgICByZXR1cm4gYXdhaXQgd29ya0FzeW5jU3RvcmFnZS5ydW4od29ya1N0b3JlLCAoKT0+d29ya1VuaXRBc3luY1N0b3JhZ2UucnVuKHJlcXVlc3RTdG9yZSwgcGFyYW1zLmhhbmRsZXIsIHJlcXVlc3QsIGV2ZW50KSk7XG4gICAgICAgICAgICAgICAgfSBmaW5hbGx5e1xuICAgICAgICAgICAgICAgICAgICAvLyBtaWRkbGV3YXJlIGNhbm5vdCBzdHJlYW0sIHNvIHdlIGNhbiBjb25zaWRlciB0aGUgcmVzcG9uc2UgY2xvc2VkXG4gICAgICAgICAgICAgICAgICAgIC8vIGFzIHNvb24gYXMgdGhlIGhhbmRsZXIgcmV0dXJucy5cbiAgICAgICAgICAgICAgICAgICAgLy8gd2UgY2FuIGRlbGF5IHJ1bm5pbmcgaXQgdW50aWwgYSBiaXQgbGF0ZXIgLS1cbiAgICAgICAgICAgICAgICAgICAgLy8gaWYgaXQncyBuZWVkZWQsIHdlJ2xsIGhhdmUgYSBgd2FpdFVudGlsYCBsb2NrIGFueXdheS5cbiAgICAgICAgICAgICAgICAgICAgc2V0VGltZW91dCgoKT0+e1xuICAgICAgICAgICAgICAgICAgICAgICAgY2xvc2VDb250cm9sbGVyLmRpc3BhdGNoQ2xvc2UoKTtcbiAgICAgICAgICAgICAgICAgICAgfSwgMCk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfSk7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIHBhcmFtcy5oYW5kbGVyKHJlcXVlc3QsIGV2ZW50KTtcbiAgICB9KTtcbiAgICAvLyBjaGVjayBpZiByZXNwb25zZSBpcyBhIFJlc3BvbnNlIG9iamVjdFxuICAgIGlmIChyZXNwb25zZSAmJiAhKHJlc3BvbnNlIGluc3RhbmNlb2YgUmVzcG9uc2UpKSB7XG4gICAgICAgIHRocm93IE9iamVjdC5kZWZpbmVQcm9wZXJ0eShuZXcgVHlwZUVycm9yKCdFeHBlY3RlZCBhbiBpbnN0YW5jZSBvZiBSZXNwb25zZSB0byBiZSByZXR1cm5lZCcpLCBcIl9fTkVYVF9FUlJPUl9DT0RFXCIsIHtcbiAgICAgICAgICAgIHZhbHVlOiBcIkU1NjdcIixcbiAgICAgICAgICAgIGVudW1lcmFibGU6IGZhbHNlLFxuICAgICAgICAgICAgY29uZmlndXJhYmxlOiB0cnVlXG4gICAgICAgIH0pO1xuICAgIH1cbiAgICBpZiAocmVzcG9uc2UgJiYgY29va2llc0Zyb21SZXNwb25zZSkge1xuICAgICAgICByZXNwb25zZS5oZWFkZXJzLnNldCgnc2V0LWNvb2tpZScsIGNvb2tpZXNGcm9tUmVzcG9uc2UpO1xuICAgIH1cbiAgICAvKipcbiAgICogRm9yIHJld3JpdGVzIHdlIG11c3QgYWx3YXlzIGluY2x1ZGUgdGhlIGxvY2FsZSBpbiB0aGUgZmluYWwgcGF0aG5hbWVcbiAgICogc28gd2UgcmUtY3JlYXRlIHRoZSBOZXh0VVJMIGZvcmNpbmcgaXQgdG8gaW5jbHVkZSBpdCB3aGVuIHRoZSBpdCBpc1xuICAgKiBhbiBpbnRlcm5hbCByZXdyaXRlLiBBbHNvIHdlIG1ha2Ugc3VyZSB0aGUgb3V0Z29pbmcgcmV3cml0ZSBVUkwgaXNcbiAgICogYSBkYXRhIFVSTCBpZiB0aGUgcmVxdWVzdCB3YXMgYSBkYXRhIHJlcXVlc3QuXG4gICAqLyBjb25zdCByZXdyaXRlID0gcmVzcG9uc2UgPT0gbnVsbCA/IHZvaWQgMCA6IHJlc3BvbnNlLmhlYWRlcnMuZ2V0KCd4LW1pZGRsZXdhcmUtcmV3cml0ZScpO1xuICAgIGlmIChyZXNwb25zZSAmJiByZXdyaXRlICYmIChpc1JTQ1JlcXVlc3QgfHwgIWlzRWRnZVJlbmRlcmluZykpIHtcbiAgICAgICAgY29uc3QgZGVzdGluYXRpb24gPSBuZXcgTmV4dFVSTChyZXdyaXRlLCB7XG4gICAgICAgICAgICBmb3JjZUxvY2FsZTogdHJ1ZSxcbiAgICAgICAgICAgIGhlYWRlcnM6IHBhcmFtcy5yZXF1ZXN0LmhlYWRlcnMsXG4gICAgICAgICAgICBuZXh0Q29uZmlnOiBwYXJhbXMucmVxdWVzdC5uZXh0Q29uZmlnXG4gICAgICAgIH0pO1xuICAgICAgICBpZiAoIXByb2Nlc3MuZW52Ll9fTkVYVF9OT19NSURETEVXQVJFX1VSTF9OT1JNQUxJWkUgJiYgIWlzRWRnZVJlbmRlcmluZykge1xuICAgICAgICAgICAgaWYgKGRlc3RpbmF0aW9uLmhvc3QgPT09IHJlcXVlc3QubmV4dFVybC5ob3N0KSB7XG4gICAgICAgICAgICAgICAgZGVzdGluYXRpb24uYnVpbGRJZCA9IGJ1aWxkSWQgfHwgZGVzdGluYXRpb24uYnVpbGRJZDtcbiAgICAgICAgICAgICAgICByZXNwb25zZS5oZWFkZXJzLnNldCgneC1taWRkbGV3YXJlLXJld3JpdGUnLCBTdHJpbmcoZGVzdGluYXRpb24pKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICAvKipcbiAgICAgKiBXaGVuIHRoZSByZXF1ZXN0IGlzIGEgZGF0YSByZXF1ZXN0IHdlIG11c3Qgc2hvdyBpZiB0aGVyZSB3YXMgYSByZXdyaXRlXG4gICAgICogd2l0aCBhbiBpbnRlcm5hbCBoZWFkZXIgc28gdGhlIGNsaWVudCBrbm93cyB3aGljaCBjb21wb25lbnQgdG8gbG9hZFxuICAgICAqIGZyb20gdGhlIGRhdGEgcmVxdWVzdC5cbiAgICAgKi8gY29uc3QgeyB1cmw6IHJlbGF0aXZlRGVzdGluYXRpb24sIGlzUmVsYXRpdmUgfSA9IHBhcnNlUmVsYXRpdmVVUkwoZGVzdGluYXRpb24udG9TdHJpbmcoKSwgcmVxdWVzdFVSTC50b1N0cmluZygpKTtcbiAgICAgICAgaWYgKCFpc0VkZ2VSZW5kZXJpbmcgJiYgaXNOZXh0RGF0YVJlcXVlc3QgJiYgLy8gaWYgdGhlIHJld3JpdGUgaXMgZXh0ZXJuYWwgYW5kIGV4dGVybmFsIHJld3JpdGVcbiAgICAgICAgLy8gcmVzb2x2aW5nIGNvbmZpZyBpcyBlbmFibGVkIGRvbid0IGFkZCB0aGlzIGhlYWRlclxuICAgICAgICAvLyBzbyB0aGUgdXBzdHJlYW0gYXBwIGNhbiBzZXQgaXQgaW5zdGVhZFxuICAgICAgICAhKHByb2Nlc3MuZW52Ll9fTkVYVF9FWFRFUk5BTF9NSURETEVXQVJFX1JFV1JJVEVfUkVTT0xWRSAmJiByZWxhdGl2ZURlc3RpbmF0aW9uLm1hdGNoKC9odHRwKHMpPzpcXC9cXC8vKSkpIHtcbiAgICAgICAgICAgIHJlc3BvbnNlLmhlYWRlcnMuc2V0KCd4LW5leHRqcy1yZXdyaXRlJywgcmVsYXRpdmVEZXN0aW5hdGlvbik7XG4gICAgICAgIH1cbiAgICAgICAgLy8gSWYgdGhpcyBpcyBhbiBSU0MgcmVxdWVzdCwgYW5kIHRoZSBwYXRobmFtZSBvciBzZWFyY2ggaGFzIGNoYW5nZWQsIGFuZFxuICAgICAgICAvLyB0aGlzIGlzbid0IGFuIGV4dGVybmFsIHJld3JpdGUsIHdlIG5lZWQgdG8gc2V0IHRoZSByZXdyaXR0ZW4gcGF0aG5hbWUgYW5kXG4gICAgICAgIC8vIHF1ZXJ5IGhlYWRlcnMuXG4gICAgICAgIGlmIChpc1JTQ1JlcXVlc3QgJiYgaXNSZWxhdGl2ZSkge1xuICAgICAgICAgICAgaWYgKHJlcXVlc3RVUkwucGF0aG5hbWUgIT09IGRlc3RpbmF0aW9uLnBhdGhuYW1lKSB7XG4gICAgICAgICAgICAgICAgcmVzcG9uc2UuaGVhZGVycy5zZXQoTkVYVF9SRVdSSVRURU5fUEFUSF9IRUFERVIsIGRlc3RpbmF0aW9uLnBhdGhuYW1lKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmIChyZXF1ZXN0VVJMLnNlYXJjaCAhPT0gZGVzdGluYXRpb24uc2VhcmNoKSB7XG4gICAgICAgICAgICAgICAgcmVzcG9uc2UuaGVhZGVycy5zZXQoTkVYVF9SRVdSSVRURU5fUVVFUllfSEVBREVSLCAvLyByZW1vdmUgdGhlIGxlYWRpbmcgPyBmcm9tIHRoZSBzZWFyY2ggc3RyaW5nXG4gICAgICAgICAgICAgICAgZGVzdGluYXRpb24uc2VhcmNoLnNsaWNlKDEpKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgIH1cbiAgICAvKipcbiAgICogRm9yIHJlZGlyZWN0cyB3ZSB3aWxsIG5vdCBpbmNsdWRlIHRoZSBsb2NhbGUgaW4gY2FzZSB3aGVuIGl0IGlzIHRoZVxuICAgKiBkZWZhdWx0IGFuZCB3ZSBtdXN0IGFsc28gbWFrZSBzdXJlIHRoZSBvdXRnb2luZyBVUkwgaXMgYSBkYXRhIG9uZSBpZlxuICAgKiB0aGUgaW5jb21pbmcgcmVxdWVzdCB3YXMgYSBkYXRhIHJlcXVlc3QuXG4gICAqLyBjb25zdCByZWRpcmVjdCA9IHJlc3BvbnNlID09IG51bGwgPyB2b2lkIDAgOiByZXNwb25zZS5oZWFkZXJzLmdldCgnTG9jYXRpb24nKTtcbiAgICBpZiAocmVzcG9uc2UgJiYgcmVkaXJlY3QgJiYgIWlzRWRnZVJlbmRlcmluZykge1xuICAgICAgICBjb25zdCByZWRpcmVjdFVSTCA9IG5ldyBOZXh0VVJMKHJlZGlyZWN0LCB7XG4gICAgICAgICAgICBmb3JjZUxvY2FsZTogZmFsc2UsXG4gICAgICAgICAgICBoZWFkZXJzOiBwYXJhbXMucmVxdWVzdC5oZWFkZXJzLFxuICAgICAgICAgICAgbmV4dENvbmZpZzogcGFyYW1zLnJlcXVlc3QubmV4dENvbmZpZ1xuICAgICAgICB9KTtcbiAgICAgICAgLyoqXG4gICAgICogUmVzcG9uc2VzIGNyZWF0ZWQgZnJvbSByZWRpcmVjdHMgaGF2ZSBpbW11dGFibGUgaGVhZGVycyBzbyB3ZSBoYXZlXG4gICAgICogdG8gY2xvbmUgdGhlIHJlc3BvbnNlIHRvIGJlIGFibGUgdG8gbW9kaWZ5IGl0LlxuICAgICAqLyByZXNwb25zZSA9IG5ldyBSZXNwb25zZShyZXNwb25zZS5ib2R5LCByZXNwb25zZSk7XG4gICAgICAgIGlmICghcHJvY2Vzcy5lbnYuX19ORVhUX05PX01JRERMRVdBUkVfVVJMX05PUk1BTElaRSkge1xuICAgICAgICAgICAgaWYgKHJlZGlyZWN0VVJMLmhvc3QgPT09IHJlcXVlc3RVUkwuaG9zdCkge1xuICAgICAgICAgICAgICAgIHJlZGlyZWN0VVJMLmJ1aWxkSWQgPSBidWlsZElkIHx8IHJlZGlyZWN0VVJMLmJ1aWxkSWQ7XG4gICAgICAgICAgICAgICAgcmVzcG9uc2UuaGVhZGVycy5zZXQoJ0xvY2F0aW9uJywgcmVkaXJlY3RVUkwudG9TdHJpbmcoKSk7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgLyoqXG4gICAgICogV2hlbiB0aGUgcmVxdWVzdCBpcyBhIGRhdGEgcmVxdWVzdCB3ZSBjYW4ndCB1c2UgdGhlIGxvY2F0aW9uIGhlYWRlciBhc1xuICAgICAqIGl0IG1heSBlbmQgdXAgd2l0aCBDT1JTIGVycm9yLiBJbnN0ZWFkIHdlIG1hcCB0byBhbiBpbnRlcm5hbCBoZWFkZXIgc29cbiAgICAgKiB0aGUgY2xpZW50IGtub3dzIHRoZSBkZXN0aW5hdGlvbi5cbiAgICAgKi8gaWYgKGlzTmV4dERhdGFSZXF1ZXN0KSB7XG4gICAgICAgICAgICByZXNwb25zZS5oZWFkZXJzLmRlbGV0ZSgnTG9jYXRpb24nKTtcbiAgICAgICAgICAgIHJlc3BvbnNlLmhlYWRlcnMuc2V0KCd4LW5leHRqcy1yZWRpcmVjdCcsIGdldFJlbGF0aXZlVVJMKHJlZGlyZWN0VVJMLnRvU3RyaW5nKCksIHJlcXVlc3RVUkwudG9TdHJpbmcoKSkpO1xuICAgICAgICB9XG4gICAgfVxuICAgIGNvbnN0IGZpbmFsUmVzcG9uc2UgPSByZXNwb25zZSA/IHJlc3BvbnNlIDogTmV4dFJlc3BvbnNlLm5leHQoKTtcbiAgICAvLyBGbGlnaHQgaGVhZGVycyBhcmUgbm90IG92ZXJyaWRhYmxlIC8gcmVtb3ZhYmxlIHNvIHRoZXkgYXJlIGFwcGxpZWQgYXQgdGhlIGVuZC5cbiAgICBjb25zdCBtaWRkbGV3YXJlT3ZlcnJpZGVIZWFkZXJzID0gZmluYWxSZXNwb25zZS5oZWFkZXJzLmdldCgneC1taWRkbGV3YXJlLW92ZXJyaWRlLWhlYWRlcnMnKTtcbiAgICBjb25zdCBvdmVyd3JpdHRlbkhlYWRlcnMgPSBbXTtcbiAgICBpZiAobWlkZGxld2FyZU92ZXJyaWRlSGVhZGVycykge1xuICAgICAgICBmb3IgKGNvbnN0IFtrZXksIHZhbHVlXSBvZiBmbGlnaHRIZWFkZXJzKXtcbiAgICAgICAgICAgIGZpbmFsUmVzcG9uc2UuaGVhZGVycy5zZXQoYHgtbWlkZGxld2FyZS1yZXF1ZXN0LSR7a2V5fWAsIHZhbHVlKTtcbiAgICAgICAgICAgIG92ZXJ3cml0dGVuSGVhZGVycy5wdXNoKGtleSk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKG92ZXJ3cml0dGVuSGVhZGVycy5sZW5ndGggPiAwKSB7XG4gICAgICAgICAgICBmaW5hbFJlc3BvbnNlLmhlYWRlcnMuc2V0KCd4LW1pZGRsZXdhcmUtb3ZlcnJpZGUtaGVhZGVycycsIG1pZGRsZXdhcmVPdmVycmlkZUhlYWRlcnMgKyAnLCcgKyBvdmVyd3JpdHRlbkhlYWRlcnMuam9pbignLCcpKTtcbiAgICAgICAgfVxuICAgIH1cbiAgICByZXR1cm4ge1xuICAgICAgICByZXNwb25zZTogZmluYWxSZXNwb25zZSxcbiAgICAgICAgd2FpdFVudGlsOiBnZXRXYWl0VW50aWxQcm9taXNlRnJvbUV2ZW50KGV2ZW50KSA/PyBQcm9taXNlLnJlc29sdmUoKSxcbiAgICAgICAgZmV0Y2hNZXRyaWNzOiByZXF1ZXN0LmZldGNoTWV0cmljc1xuICAgIH07XG59XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWFkYXB0ZXIuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(middleware)/./node_modules/next/dist/esm/server/web/adapter.js\n");

/***/ }),

/***/ "(middleware)/./node_modules/next/dist/esm/server/web/spec-extension/adapters/request-cookies.js":
/*!******************************************************************************************!*\
  !*** ./node_modules/next/dist/esm/server/web/spec-extension/adapters/request-cookies.js ***!
  \******************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MutableRequestCookiesAdapter: () => (/* binding */ MutableRequestCookiesAdapter),\n/* harmony export */   ReadonlyRequestCookiesError: () => (/* binding */ ReadonlyRequestCookiesError),\n/* harmony export */   RequestCookiesAdapter: () => (/* binding */ RequestCookiesAdapter),\n/* harmony export */   appendMutableCookies: () => (/* binding */ appendMutableCookies),\n/* harmony export */   areCookiesMutableInCurrentPhase: () => (/* binding */ areCookiesMutableInCurrentPhase),\n/* harmony export */   getModifiedCookieValues: () => (/* binding */ getModifiedCookieValues),\n/* harmony export */   responseCookiesToRequestCookies: () => (/* binding */ responseCookiesToRequestCookies),\n/* harmony export */   wrapWithMutableAccessCheck: () => (/* binding */ wrapWithMutableAccessCheck)\n/* harmony export */ });\n/* harmony import */ var _cookies__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../cookies */ \"(middleware)/./node_modules/next/dist/esm/server/web/spec-extension/cookies.js\");\n/* harmony import */ var _reflect__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./reflect */ \"(middleware)/./node_modules/next/dist/esm/server/web/spec-extension/adapters/reflect.js\");\n/* harmony import */ var _app_render_work_async_storage_external__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../app-render/work-async-storage.external */ \"(shared)/./node_modules/next/dist/esm/server/app-render/work-async-storage.external.js?73ab\");\n/* harmony import */ var _app_render_work_unit_async_storage_external__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../app-render/work-unit-async-storage.external */ \"(shared)/./node_modules/next/dist/esm/server/app-render/work-unit-async-storage.external.js?418e\");\n\n\n\n\n\n/**\n * @internal\n */ class ReadonlyRequestCookiesError extends Error {\n    constructor(){\n        super('Cookies can only be modified in a Server Action or Route Handler. Read more: https://nextjs.org/docs/app/api-reference/functions/cookies#options');\n    }\n    static callable() {\n        throw new ReadonlyRequestCookiesError();\n    }\n}\nclass RequestCookiesAdapter {\n    static seal(cookies) {\n        return new Proxy(cookies, {\n            get (target, prop, receiver) {\n                switch(prop){\n                    case 'clear':\n                    case 'delete':\n                    case 'set':\n                        return ReadonlyRequestCookiesError.callable;\n                    default:\n                        return _reflect__WEBPACK_IMPORTED_MODULE_1__.ReflectAdapter.get(target, prop, receiver);\n                }\n            }\n        });\n    }\n}\nconst SYMBOL_MODIFY_COOKIE_VALUES = Symbol.for('next.mutated.cookies');\nfunction getModifiedCookieValues(cookies) {\n    const modified = cookies[SYMBOL_MODIFY_COOKIE_VALUES];\n    if (!modified || !Array.isArray(modified) || modified.length === 0) {\n        return [];\n    }\n    return modified;\n}\nfunction appendMutableCookies(headers, mutableCookies) {\n    const modifiedCookieValues = getModifiedCookieValues(mutableCookies);\n    if (modifiedCookieValues.length === 0) {\n        return false;\n    }\n    // Return a new response that extends the response with\n    // the modified cookies as fallbacks. `res` cookies\n    // will still take precedence.\n    const resCookies = new _cookies__WEBPACK_IMPORTED_MODULE_0__.ResponseCookies(headers);\n    const returnedCookies = resCookies.getAll();\n    // Set the modified cookies as fallbacks.\n    for (const cookie of modifiedCookieValues){\n        resCookies.set(cookie);\n    }\n    // Set the original cookies as the final values.\n    for (const cookie of returnedCookies){\n        resCookies.set(cookie);\n    }\n    return true;\n}\nclass MutableRequestCookiesAdapter {\n    static wrap(cookies, onUpdateCookies) {\n        const responseCookies = new _cookies__WEBPACK_IMPORTED_MODULE_0__.ResponseCookies(new Headers());\n        for (const cookie of cookies.getAll()){\n            responseCookies.set(cookie);\n        }\n        let modifiedValues = [];\n        const modifiedCookies = new Set();\n        const updateResponseCookies = ()=>{\n            // TODO-APP: change method of getting workStore\n            const workStore = _app_render_work_async_storage_external__WEBPACK_IMPORTED_MODULE_2__.workAsyncStorage.getStore();\n            if (workStore) {\n                workStore.pathWasRevalidated = true;\n            }\n            const allCookies = responseCookies.getAll();\n            modifiedValues = allCookies.filter((c)=>modifiedCookies.has(c.name));\n            if (onUpdateCookies) {\n                const serializedCookies = [];\n                for (const cookie of modifiedValues){\n                    const tempCookies = new _cookies__WEBPACK_IMPORTED_MODULE_0__.ResponseCookies(new Headers());\n                    tempCookies.set(cookie);\n                    serializedCookies.push(tempCookies.toString());\n                }\n                onUpdateCookies(serializedCookies);\n            }\n        };\n        const wrappedCookies = new Proxy(responseCookies, {\n            get (target, prop, receiver) {\n                switch(prop){\n                    // A special symbol to get the modified cookie values\n                    case SYMBOL_MODIFY_COOKIE_VALUES:\n                        return modifiedValues;\n                    // TODO: Throw error if trying to set a cookie after the response\n                    // headers have been set.\n                    case 'delete':\n                        return function(...args) {\n                            modifiedCookies.add(typeof args[0] === 'string' ? args[0] : args[0].name);\n                            try {\n                                target.delete(...args);\n                                return wrappedCookies;\n                            } finally{\n                                updateResponseCookies();\n                            }\n                        };\n                    case 'set':\n                        return function(...args) {\n                            modifiedCookies.add(typeof args[0] === 'string' ? args[0] : args[0].name);\n                            try {\n                                target.set(...args);\n                                return wrappedCookies;\n                            } finally{\n                                updateResponseCookies();\n                            }\n                        };\n                    default:\n                        return _reflect__WEBPACK_IMPORTED_MODULE_1__.ReflectAdapter.get(target, prop, receiver);\n                }\n            }\n        });\n        return wrappedCookies;\n    }\n}\nfunction wrapWithMutableAccessCheck(responseCookies) {\n    const wrappedCookies = new Proxy(responseCookies, {\n        get (target, prop, receiver) {\n            switch(prop){\n                case 'delete':\n                    return function(...args) {\n                        ensureCookiesAreStillMutable('cookies().delete');\n                        target.delete(...args);\n                        return wrappedCookies;\n                    };\n                case 'set':\n                    return function(...args) {\n                        ensureCookiesAreStillMutable('cookies().set');\n                        target.set(...args);\n                        return wrappedCookies;\n                    };\n                default:\n                    return _reflect__WEBPACK_IMPORTED_MODULE_1__.ReflectAdapter.get(target, prop, receiver);\n            }\n        }\n    });\n    return wrappedCookies;\n}\nfunction areCookiesMutableInCurrentPhase(requestStore) {\n    return requestStore.phase === 'action';\n}\n/** Ensure that cookies() starts throwing on mutation\n * if we changed phases and can no longer mutate.\n *\n * This can happen when going:\n *   'render' -> 'after'\n *   'action' -> 'render'\n * */ function ensureCookiesAreStillMutable(callingExpression) {\n    const requestStore = (0,_app_render_work_unit_async_storage_external__WEBPACK_IMPORTED_MODULE_3__.getExpectedRequestStore)(callingExpression);\n    if (!areCookiesMutableInCurrentPhase(requestStore)) {\n        // TODO: maybe we can give a more precise error message based on callingExpression?\n        throw new ReadonlyRequestCookiesError();\n    }\n}\nfunction responseCookiesToRequestCookies(responseCookies) {\n    const requestCookies = new _cookies__WEBPACK_IMPORTED_MODULE_0__.RequestCookies(new Headers());\n    for (const cookie of responseCookies.getAll()){\n        requestCookies.set(cookie);\n    }\n    return requestCookies;\n}\n\n//# sourceMappingURL=request-cookies.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./node_modules/next/dist/esm/server/web/spec-extension/adapters/request-cookies.js\n");

/***/ })

});