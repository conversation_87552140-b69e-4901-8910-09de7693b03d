import { type NextRequest, NextResponse } from 'next/server';
import { ApiKeyAuthMiddleware } from '@/lib/userApiKeys/authMiddleware';
import { z } from 'zod';

// OpenAI-compatible external API endpoint for user-generated API keys
// This endpoint accepts requests from external applications using user-generated API keys
// Compatible with OpenAI SDK and other OpenAI-compatible clients

const OpenAICompatibleRequestSchema = z.object({
  model: z.string().optional().default('gpt-3.5-turbo'), // OpenAI compatibility
  messages: z.array(
    z.object({
      role: z.enum(['user', 'assistant', 'system']),
      content: z.union([
        z.string(),
        z.array(z.any()) // Support multimodal content
      ]),
    })
  ).min(1, { message: "Messages array cannot be empty." }),
  stream: z.boolean().optional().default(false),
  temperature: z.number().min(0).max(2).optional(),
  max_tokens: z.number().int().positive().optional(),
  top_p: z.number().min(0).max(1).optional(),
  frequency_penalty: z.number().min(-2).max(2).optional(),
  presence_penalty: z.number().min(-2).max(2).optional(),
  stop: z.union([z.string(), z.array(z.string())]).optional(),
  n: z.number().int().positive().optional().default(1),
  // RouKey-specific extensions
  role: z.string().optional(), // For role-based routing
}).catchall(z.any()); // Allow additional OpenAI parameters

const authMiddleware = new ApiKeyAuthMiddleware();

// Helper function to determine if request should use async processing
function shouldUseAsyncProcessing(requestData: any): {
  recommend: boolean;
  reason?: string;
  estimatedMinutes?: number;
} {
  let score = 0;
  let reasons: string[] = [];

  // Check message complexity
  const totalLength = requestData.messages?.reduce((sum: number, msg: any) =>
    sum + (msg.content?.length || 0), 0) || 0;

  if (totalLength > 5000) {
    score += 2;
    reasons.push('Long input content');
  }

  // Check for role-based routing (indicates multi-role orchestration)
  if (requestData.role) {
    score += 3;
    reasons.push('Role-based routing (multi-role orchestration)');
  }

  // Check for high max_tokens
  if (requestData.max_tokens && requestData.max_tokens > 2000) {
    score += 2;
    reasons.push('High token output requested');
  }

  // Check for creative temperature (more complex generation)
  if (requestData.temperature && requestData.temperature > 1.0) {
    score += 1;
    reasons.push('High creativity setting');
  }

  // Keywords that suggest complex tasks
  const complexKeywords = [
    'brainstorm', 'analyze', 'research', 'comprehensive', 'detailed',
    'step by step', 'multi-step', 'write a', 'create a', 'develop',
    'explain', 'tutorial', 'guide', 'example', 'code', 'program',
    'algorithm', 'implementation', 'design', 'build'
  ];
  const hasComplexKeywords = requestData.messages?.some((msg: any) =>
    complexKeywords.some(keyword =>
      msg.content?.toLowerCase().includes(keyword)
    )
  );

  if (hasComplexKeywords) {
    score += 3; // Increased from 2 to 3
    reasons.push('Complex task keywords detected');
  }

  // Check for multiple tasks in one request (e.g., "brainstorm AND write code")
  const taskWords = ['brainstorm', 'write', 'create', 'develop', 'analyze', 'explain', 'code'];
  const taskCount = taskWords.filter(word =>
    requestData.messages?.some((msg: any) =>
      msg.content?.toLowerCase().includes(word)
    )
  ).length;

  if (taskCount >= 2) {
    score += 2;
    reasons.push('Multiple tasks in one request');
  }

  const recommend = score >= 3; // Lowered from 4 to 3
  const estimatedMinutes = Math.min(Math.max(2, Math.ceil(score * 1.5)), 15);

  return {
    recommend,
    reason: recommend ? reasons.join(', ') : undefined,
    estimatedMinutes: recommend ? estimatedMinutes : undefined
  };
}

export async function POST(request: NextRequest) {
  try {
    // 1. Authenticate using user-generated API key
    const authResult = await authMiddleware.authenticateRequest(request);
    
    if (!authResult.success) {
      // Return OpenAI-compatible error format
      return NextResponse.json(
        {
          error: {
            message: authResult.error,
            type: 'authentication_error',
            code: 'invalid_api_key'
          }
        },
        { status: authResult.statusCode || 401 }
      );
    }

    const { userApiKey, userConfig, ipAddress } = authResult;

    // 2. Check permissions
    if (!authMiddleware.hasPermission(userApiKey!, 'chat')) {
      return NextResponse.json(
        {
          error: {
            message: 'API key does not have chat permission',
            type: 'permission_error',
            code: 'insufficient_permissions'
          }
        },
        { status: 403 }
      );
    }

    // 3. Parse and validate request body
    const rawBody = await request.json();
    const validationResult = OpenAICompatibleRequestSchema.safeParse(rawBody);

    if (!validationResult.success) {
      return NextResponse.json(
        {
          error: {
            message: 'Invalid request body',
            type: 'invalid_request_error',
            code: 'invalid_request',
            details: validationResult.error.flatten().fieldErrors
          }
        },
        { status: 400 }
      );
    }

    const requestData = validationResult.data;

    // 4. Check streaming permission if requested
    if (requestData.stream && !authMiddleware.hasPermission(userApiKey!, 'streaming')) {
      return NextResponse.json(
        {
          error: {
            message: 'API key does not have streaming permission',
            type: 'permission_error',
            code: 'streaming_not_allowed'
          }
        },
        { status: 403 }
      );
    }

    // 5. Check if request should use async processing
    // First, check if this is a multi-role task by calling internal classification
    let isMultiRoleTask = false;

    if (requestData.role || requestData.messages?.some((msg: any) =>
      msg.content && typeof msg.content === 'string' && msg.content.length > 100
    )) {
      try {
        // Quick classification check to detect multi-role tasks
        const classificationResponse = await fetch(`${process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'}/api/internal/classify-multi-role`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${process.env.ROKEY_API_ACCESS_TOKEN}`,
          },
          body: JSON.stringify({
            messages: requestData.messages,
            role: requestData.role,
            config_id: userConfig!.id
          }),
        });

        if (classificationResponse.ok) {
          const classificationResult = await classificationResponse.json();
          isMultiRoleTask = classificationResult.isMultiRole;

          if (isMultiRoleTask) {
            console.log(`[External API] Multi-role task detected by Gemini classifier - auto-routing to async processing`);
          }
        }
      } catch (error) {
        console.warn('[External API] Classification check failed, falling back to keyword detection');
      }
    }

    // Store async processing recommendations for response headers
    let asyncRecommendation: any = null;

    // If multi-role detected, recommend async processing or streaming (but still execute)
    if (isMultiRoleTask && !requestData.stream) {
      asyncRecommendation = {
        type: 'multi_role_detected',
        reason: 'Gemini classifier detected this task requires multiple specialized roles working together',
        async_submit_url: `${process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'}/api/external/v1/async/submit`,
        estimated_time_minutes: 5,
        streaming_recommendation: 'Consider using stream=true for better responsiveness with multi-role tasks',
        benefits: [
          'No timeout limits for complex multi-role orchestration',
          'Progress tracking with role detection',
          'Webhook notifications when complete',
          'Proper handling of role coordination'
        ]
      };
      console.log(`[External API] Multi-role task detected - recommending async processing but proceeding with execution`);
    }

    // Fallback to keyword-based complexity detection for non-multi-role tasks
    if (!isMultiRoleTask) {
      const shouldUseAsync = shouldUseAsyncProcessing(requestData);
      if (shouldUseAsync.recommend && !requestData.stream) {
        asyncRecommendation = {
          type: 'complexity_warning',
          reason: shouldUseAsync.reason,
          async_submit_url: `${process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'}/api/external/v1/async/submit`,
          estimated_time_minutes: shouldUseAsync.estimatedMinutes,
          streaming_recommendation: 'Consider using stream=true for better responsiveness',
          benefits: [
            'No timeout limits',
            'Progress tracking',
            'Webhook notifications',
            'Better handling of complex tasks'
          ]
        };
        console.log(`[External API] Complex task detected - recommending async processing but proceeding with execution`);
      }
    }

    // Prepare request for internal RouKey API
    const internalRequest = {
      custom_api_config_id: userConfig!.id,
      messages: requestData.messages,
      stream: requestData.stream,
      temperature: requestData.temperature,
      max_tokens: requestData.max_tokens,
      role: requestData.role, // RouKey-specific role routing
      model: requestData.model,
      // Pass through other OpenAI parameters
      top_p: requestData.top_p,
      frequency_penalty: requestData.frequency_penalty,
      presence_penalty: requestData.presence_penalty,
      stop: requestData.stop,
      n: requestData.n,
      // Pass user context for RLS
      _internal_user_id: userConfig!.user_id,
    };

    // 6. Call internal RouKey API with timeout
    const internalApiUrl = new URL('/api/v1/chat/completions', request.url);

    // Create AbortController for timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 55000); // 55s timeout (5s buffer for Vercel)

    let internalResponse: Response;
    try {
      internalResponse = await fetch(internalApiUrl.toString(), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${process.env.ROKEY_API_ACCESS_TOKEN}`,
          'X-Forwarded-For': ipAddress || '',
          'X-User-API-Key-ID': userApiKey!.id,
          'X-External-Request': 'true',
        },
        body: JSON.stringify(internalRequest),
        signal: controller.signal,
      });
    } catch (error: any) {
      clearTimeout(timeoutId);
      if (error.name === 'AbortError') {
        // Handle timeout - suggest async processing
        return NextResponse.json(
          {
            error: {
              message: 'Request timeout. For complex multi-role tasks, consider using async processing or breaking down the request.',
              type: 'timeout_error',
              code: 'request_timeout',
              suggestion: 'Try reducing complexity or use streaming for better responsiveness'
            }
          },
          { status: 408 }
        );
      }
      throw error; // Re-throw other errors
    }
    clearTimeout(timeoutId);

    // 7. Extract role information for logging (before consuming response)
    let rolesUsed: string[] = [];
    let actualModelUsed = requestData.model;

    // For non-streaming, we can peek at the response to extract role info
    if (!requestData.stream && internalResponse.ok) {
      try {
        const responseClone = internalResponse.clone();
        const responseData = await responseClone.json();

        // Extract roles from response metadata
        if (responseData.rokey_metadata?.roles_used) {
          rolesUsed = responseData.rokey_metadata.roles_used;
        }
        if (responseData.model) {
          actualModelUsed = responseData.model;
        }
      } catch (error) {
        console.warn('Could not extract role metadata:', error);
      }
    }

    // Log usage asynchronously
    authMiddleware.logApiUsage(
      userApiKey!,
      request,
      {
        statusCode: internalResponse.status,
        modelUsed: actualModelUsed,
        providerUsed: rolesUsed.length > 0 ? rolesUsed.join(', ') : undefined,
      },
      ipAddress
    ).catch((error: any) => {
      console.error('Failed to log API usage:', error);
    });

    // 8. Handle response based on streaming
    if (requestData.stream) {
      // For streaming responses, pass through the stream with proper headers
      return new Response(internalResponse.body, {
        status: internalResponse.status,
        headers: {
          'Content-Type': 'text/event-stream',
          'Cache-Control': 'no-cache',
          'Connection': 'keep-alive',
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'POST, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        },
      });
    } else {
      // For non-streaming responses, return JSON
      const responseData = await internalResponse.json();

      // Ensure OpenAI-compatible response format
      if (internalResponse.ok) {
        // Enhance response with role information for external API
        const enhancedResponse = {
          ...responseData,
          // Add RouKey-specific metadata while maintaining OpenAI compatibility
          rokey_metadata: {
            roles_used: responseData.rokey_metadata?.roles_used || [],
            routing_strategy: userConfig!.routing_strategy,
            config_name: userConfig!.name,
            api_key_name: userApiKey!.key_name,
            ...(responseData.rokey_metadata || {})
          }
        };

        return NextResponse.json(enhancedResponse, {
          status: internalResponse.status,
          headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'POST, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',
            'X-RouKey-Roles-Used': rolesUsed.join(', ') || 'none',
            'X-RouKey-Config': userConfig!.name,
          }
        });
      } else {
        // Convert internal errors to OpenAI format
        return NextResponse.json(
          {
            error: {
              message: responseData.error || 'Internal server error',
              type: 'server_error',
              code: 'internal_error'
            }
          },
          { status: internalResponse.status }
        );
      }
    }

  } catch (error) {
    console.error('Error in external chat completions API:', error);
    return NextResponse.json(
      {
        error: {
          message: 'Internal server error',
          type: 'server_error',
          code: 'internal_error'
        }
      },
      { status: 500 }
    );
  }
}

// Handle CORS preflight requests
export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',
      'Access-Control-Max-Age': '86400',
    },
  });
}
