{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|public\\/).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|public/).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "Ynh4vZIlB48nWqNFEGVkT", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "JC0JWGNaajZip1Z2YjZMoKnLk5xyDf+WEszmrjt8X4w=", "__NEXT_PREVIEW_MODE_ID": "97a1ef8d78eb3541089f181c8f4c597d", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "2283e4ca8ea7ed8a0b02c556c3948133a4a25906b4ec58eb7c9eb63b554e5d6b", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "c4b2af50493b8c0289673205eb5457339cc7485c39a7315773bbc0195c64785e"}}}, "functions": {"/api/external/v1/chat/completions/route": {"files": ["server/server-reference-manifest.js", "server/app/api/external/v1/chat/completions/route_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/app/api/external/v1/chat/completions/route.js"], "name": "app/api/external/v1/chat/completions/route", "page": "/api/external/v1/chat/completions/route", "matchers": [{"regexp": "^/api/external/v1/chat/completions$", "originalSource": "/api/external/v1/chat/completions"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "Ynh4vZIlB48nWqNFEGVkT", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "JC0JWGNaajZip1Z2YjZMoKnLk5xyDf+WEszmrjt8X4w=", "__NEXT_PREVIEW_MODE_ID": "97a1ef8d78eb3541089f181c8f4c597d", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "2283e4ca8ea7ed8a0b02c556c3948133a4a25906b4ec58eb7c9eb63b554e5d6b", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "c4b2af50493b8c0289673205eb5457339cc7485c39a7315773bbc0195c64785e"}}}, "sortedMiddleware": ["/"]}